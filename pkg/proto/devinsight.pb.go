// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.2
// source: pkg/proto/devinsight.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RegisterAgentRequest Agent 注册请求
type RegisterAgentRequest struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	AgentId                 string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	AgentIp                 string                 `protobuf:"bytes,2,opt,name=agent_ip,json=agentIp,proto3" json:"agent_ip,omitempty"`
	SupportedCollectorTypes []string               `protobuf:"bytes,3,rep,name=supported_collector_types,json=supportedCollectorTypes,proto3" json:"supported_collector_types,omitempty"`
	DeviceConfig            *DeviceConfig          `protobuf:"bytes,4,opt,name=device_config,json=deviceConfig,proto3" json:"device_config,omitempty"` // Agent设备配置信息
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *RegisterAgentRequest) Reset() {
	*x = RegisterAgentRequest{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterAgentRequest) ProtoMessage() {}

func (x *RegisterAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterAgentRequest.ProtoReflect.Descriptor instead.
func (*RegisterAgentRequest) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterAgentRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *RegisterAgentRequest) GetAgentIp() string {
	if x != nil {
		return x.AgentIp
	}
	return ""
}

func (x *RegisterAgentRequest) GetSupportedCollectorTypes() []string {
	if x != nil {
		return x.SupportedCollectorTypes
	}
	return nil
}

func (x *RegisterAgentRequest) GetDeviceConfig() *DeviceConfig {
	if x != nil {
		return x.DeviceConfig
	}
	return nil
}

// DeviceConfig Agent设备配置信息
type DeviceConfig struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	MaxMemoryMb        int64                  `protobuf:"varint,1,opt,name=max_memory_mb,json=maxMemoryMb,proto3" json:"max_memory_mb,omitempty"`
	MaxCpuPercent      int32                  `protobuf:"varint,2,opt,name=max_cpu_percent,json=maxCpuPercent,proto3" json:"max_cpu_percent,omitempty"`
	MaxDiskMb          int64                  `protobuf:"varint,3,opt,name=max_disk_mb,json=maxDiskMb,proto3" json:"max_disk_mb,omitempty"`
	MaxConcurrentTasks int32                  `protobuf:"varint,4,opt,name=max_concurrent_tasks,json=maxConcurrentTasks,proto3" json:"max_concurrent_tasks,omitempty"`
	Capabilities       map[string]string      `protobuf:"bytes,5,rep,name=capabilities,proto3" json:"capabilities,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *DeviceConfig) Reset() {
	*x = DeviceConfig{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceConfig) ProtoMessage() {}

func (x *DeviceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceConfig.ProtoReflect.Descriptor instead.
func (*DeviceConfig) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{1}
}

func (x *DeviceConfig) GetMaxMemoryMb() int64 {
	if x != nil {
		return x.MaxMemoryMb
	}
	return 0
}

func (x *DeviceConfig) GetMaxCpuPercent() int32 {
	if x != nil {
		return x.MaxCpuPercent
	}
	return 0
}

func (x *DeviceConfig) GetMaxDiskMb() int64 {
	if x != nil {
		return x.MaxDiskMb
	}
	return 0
}

func (x *DeviceConfig) GetMaxConcurrentTasks() int32 {
	if x != nil {
		return x.MaxConcurrentTasks
	}
	return 0
}

func (x *DeviceConfig) GetCapabilities() map[string]string {
	if x != nil {
		return x.Capabilities
	}
	return nil
}

// RegisterAgentResponse Agent 注册响应
type RegisterAgentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterAgentResponse) Reset() {
	*x = RegisterAgentResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterAgentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterAgentResponse) ProtoMessage() {}

func (x *RegisterAgentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterAgentResponse.ProtoReflect.Descriptor instead.
func (*RegisterAgentResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{2}
}

func (x *RegisterAgentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RegisterAgentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// CollectorTaskConfig 采集任务配置
type CollectorTaskConfig struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TaskId           string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	DeviceId         string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	DeviceName       string                 `protobuf:"bytes,3,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`
	DeviceType       string                 `protobuf:"bytes,4,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	Host             string                 `protobuf:"bytes,5,opt,name=host,proto3" json:"host,omitempty"`
	Port             int32                  `protobuf:"varint,6,opt,name=port,proto3" json:"port,omitempty"`
	Username         string                 `protobuf:"bytes,7,opt,name=username,proto3" json:"username,omitempty"`
	Password         string                 `protobuf:"bytes,8,opt,name=password,proto3" json:"password,omitempty"`
	ConnectParams    map[string]string      `protobuf:"bytes,9,rep,name=connect_params,json=connectParams,proto3" json:"connect_params,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	FrequencySeconds int64                  `protobuf:"varint,10,opt,name=frequency_seconds,json=frequencySeconds,proto3" json:"frequency_seconds,omitempty"`
	CollectItems     []string               `protobuf:"bytes,11,rep,name=collect_items,json=collectItems,proto3" json:"collect_items,omitempty"`
	IsEnabled        bool                   `protobuf:"varint,12,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CollectorTaskConfig) Reset() {
	*x = CollectorTaskConfig{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CollectorTaskConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectorTaskConfig) ProtoMessage() {}

func (x *CollectorTaskConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectorTaskConfig.ProtoReflect.Descriptor instead.
func (*CollectorTaskConfig) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{3}
}

func (x *CollectorTaskConfig) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *CollectorTaskConfig) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *CollectorTaskConfig) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *CollectorTaskConfig) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *CollectorTaskConfig) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *CollectorTaskConfig) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *CollectorTaskConfig) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *CollectorTaskConfig) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CollectorTaskConfig) GetConnectParams() map[string]string {
	if x != nil {
		return x.ConnectParams
	}
	return nil
}

func (x *CollectorTaskConfig) GetFrequencySeconds() int64 {
	if x != nil {
		return x.FrequencySeconds
	}
	return 0
}

func (x *CollectorTaskConfig) GetCollectItems() []string {
	if x != nil {
		return x.CollectItems
	}
	return nil
}

func (x *CollectorTaskConfig) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

// TaskStatus 任务状态上报
type TaskStatus struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TaskId               string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Status               string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"` // running, failed, success
	ErrorMessage         string                 `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	LastCollectTimestamp int64                  `protobuf:"varint,4,opt,name=last_collect_timestamp,json=lastCollectTimestamp,proto3" json:"last_collect_timestamp,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *TaskStatus) Reset() {
	*x = TaskStatus{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskStatus) ProtoMessage() {}

func (x *TaskStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskStatus.ProtoReflect.Descriptor instead.
func (*TaskStatus) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{4}
}

func (x *TaskStatus) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TaskStatus) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *TaskStatus) GetLastCollectTimestamp() int64 {
	if x != nil {
		return x.LastCollectTimestamp
	}
	return 0
}

// MetricData 指标数据点 - 支持灵活的多维数据
type MetricData struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	DeviceId  string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	MetricKey string                 `protobuf:"bytes,2,opt,name=metric_key,json=metricKey,proto3" json:"metric_key,omitempty"`
	// Types that are valid to be assigned to ValueType:
	//
	//	*MetricData_NumericValue
	//	*MetricData_StringValue
	//	*MetricData_BooleanValue
	ValueType     isMetricData_ValueType `protobuf_oneof:"value_type"`
	Timestamp     int64                  `protobuf:"varint,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	JsonData      string                 `protobuf:"bytes,7,opt,name=json_data,json=jsonData,proto3" json:"json_data,omitempty"` // 灵活的JSON格式数据，支持复杂的多维指标
	Labels        map[string]string      `protobuf:"bytes,8,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MetricData) Reset() {
	*x = MetricData{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetricData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetricData) ProtoMessage() {}

func (x *MetricData) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetricData.ProtoReflect.Descriptor instead.
func (*MetricData) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{5}
}

func (x *MetricData) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *MetricData) GetMetricKey() string {
	if x != nil {
		return x.MetricKey
	}
	return ""
}

func (x *MetricData) GetValueType() isMetricData_ValueType {
	if x != nil {
		return x.ValueType
	}
	return nil
}

func (x *MetricData) GetNumericValue() float64 {
	if x != nil {
		if x, ok := x.ValueType.(*MetricData_NumericValue); ok {
			return x.NumericValue
		}
	}
	return 0
}

func (x *MetricData) GetStringValue() string {
	if x != nil {
		if x, ok := x.ValueType.(*MetricData_StringValue); ok {
			return x.StringValue
		}
	}
	return ""
}

func (x *MetricData) GetBooleanValue() bool {
	if x != nil {
		if x, ok := x.ValueType.(*MetricData_BooleanValue); ok {
			return x.BooleanValue
		}
	}
	return false
}

func (x *MetricData) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *MetricData) GetJsonData() string {
	if x != nil {
		return x.JsonData
	}
	return ""
}

func (x *MetricData) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type isMetricData_ValueType interface {
	isMetricData_ValueType()
}

type MetricData_NumericValue struct {
	NumericValue float64 `protobuf:"fixed64,3,opt,name=numeric_value,json=numericValue,proto3,oneof"`
}

type MetricData_StringValue struct {
	StringValue string `protobuf:"bytes,4,opt,name=string_value,json=stringValue,proto3,oneof"`
}

type MetricData_BooleanValue struct {
	BooleanValue bool `protobuf:"varint,5,opt,name=boolean_value,json=booleanValue,proto3,oneof"`
}

func (*MetricData_NumericValue) isMetricData_ValueType() {}

func (*MetricData_StringValue) isMetricData_ValueType() {}

func (*MetricData_BooleanValue) isMetricData_ValueType() {}

// SupportedMetric 支持的指标定义
type SupportedMetric struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MetricKey     string                 `protobuf:"bytes,1,opt,name=metric_key,json=metricKey,proto3" json:"metric_key,omitempty"`
	MetricName    string                 `protobuf:"bytes,2,opt,name=metric_name,json=metricName,proto3" json:"metric_name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	DataType      string                 `protobuf:"bytes,4,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"` // numeric, string, boolean, json
	Unit          string                 `protobuf:"bytes,5,opt,name=unit,proto3" json:"unit,omitempty"`
	Metadata      map[string]string      `protobuf:"bytes,6,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	IsActive      bool                   `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	CollectorType string                 `protobuf:"bytes,8,opt,name=collector_type,json=collectorType,proto3" json:"collector_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SupportedMetric) Reset() {
	*x = SupportedMetric{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SupportedMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportedMetric) ProtoMessage() {}

func (x *SupportedMetric) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportedMetric.ProtoReflect.Descriptor instead.
func (*SupportedMetric) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{6}
}

func (x *SupportedMetric) GetMetricKey() string {
	if x != nil {
		return x.MetricKey
	}
	return ""
}

func (x *SupportedMetric) GetMetricName() string {
	if x != nil {
		return x.MetricName
	}
	return ""
}

func (x *SupportedMetric) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SupportedMetric) GetDataType() string {
	if x != nil {
		return x.DataType
	}
	return ""
}

func (x *SupportedMetric) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *SupportedMetric) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SupportedMetric) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *SupportedMetric) GetCollectorType() string {
	if x != nil {
		return x.CollectorType
	}
	return ""
}

// LogEntry 日志条目
type LogEntry struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	LogLevel      string                 `protobuf:"bytes,2,opt,name=log_level,json=logLevel,proto3" json:"log_level,omitempty"` // DEBUG, INFO, WARN, ERROR
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	Timestamp     int64                  `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Source        string                 `protobuf:"bytes,5,opt,name=source,proto3" json:"source,omitempty"`                                                                           // 日志来源：agent, collector, etc.
	Fields        map[string]string      `protobuf:"bytes,6,rep,name=fields,proto3" json:"fields,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 额外的结构化字段
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogEntry) Reset() {
	*x = LogEntry{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogEntry) ProtoMessage() {}

func (x *LogEntry) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogEntry.ProtoReflect.Descriptor instead.
func (*LogEntry) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{7}
}

func (x *LogEntry) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *LogEntry) GetLogLevel() string {
	if x != nil {
		return x.LogLevel
	}
	return ""
}

func (x *LogEntry) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LogEntry) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *LogEntry) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *LogEntry) GetFields() map[string]string {
	if x != nil {
		return x.Fields
	}
	return nil
}

// StreamLogDataResponse 日志数据上报响应
type StreamLogDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ReceivedCount int32                  `protobuf:"varint,3,opt,name=received_count,json=receivedCount,proto3" json:"received_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamLogDataResponse) Reset() {
	*x = StreamLogDataResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamLogDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamLogDataResponse) ProtoMessage() {}

func (x *StreamLogDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamLogDataResponse.ProtoReflect.Descriptor instead.
func (*StreamLogDataResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{8}
}

func (x *StreamLogDataResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *StreamLogDataResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StreamLogDataResponse) GetReceivedCount() int32 {
	if x != nil {
		return x.ReceivedCount
	}
	return 0
}

// StreamMetricDataResponse 指标数据上报响应
type StreamMetricDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ReceivedCount int32                  `protobuf:"varint,3,opt,name=received_count,json=receivedCount,proto3" json:"received_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamMetricDataResponse) Reset() {
	*x = StreamMetricDataResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamMetricDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamMetricDataResponse) ProtoMessage() {}

func (x *StreamMetricDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamMetricDataResponse.ProtoReflect.Descriptor instead.
func (*StreamMetricDataResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{9}
}

func (x *StreamMetricDataResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *StreamMetricDataResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StreamMetricDataResponse) GetReceivedCount() int32 {
	if x != nil {
		return x.ReceivedCount
	}
	return 0
}

// PluginRequest Agent 请求插件
type PluginRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgentId       string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	PluginName    string                 `protobuf:"bytes,2,opt,name=plugin_name,json=pluginName,proto3" json:"plugin_name,omitempty"`
	PluginVersion string                 `protobuf:"bytes,3,opt,name=plugin_version,json=pluginVersion,proto3" json:"plugin_version,omitempty"` // 可选，如果为空则获取最新版本
	DeviceType    string                 `protobuf:"bytes,4,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`          // 设备类型，用于确定插件兼容性
	Architecture  string                 `protobuf:"bytes,5,opt,name=architecture,proto3" json:"architecture,omitempty"`                        // 系统架构：amd64, arm64, etc.
	Os            string                 `protobuf:"bytes,6,opt,name=os,proto3" json:"os,omitempty"`                                            // 操作系统：linux, windows, darwin
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PluginRequest) Reset() {
	*x = PluginRequest{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginRequest) ProtoMessage() {}

func (x *PluginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginRequest.ProtoReflect.Descriptor instead.
func (*PluginRequest) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{10}
}

func (x *PluginRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *PluginRequest) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *PluginRequest) GetPluginVersion() string {
	if x != nil {
		return x.PluginVersion
	}
	return ""
}

func (x *PluginRequest) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *PluginRequest) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *PluginRequest) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

// PluginResponse Control Plane 响应插件请求
type PluginResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Metadata      *PluginMetadata        `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	PluginBinary  []byte                 `protobuf:"bytes,4,opt,name=plugin_binary,json=pluginBinary,proto3" json:"plugin_binary,omitempty"` // 插件二进制文件
	Checksum      string                 `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`                             // 插件文件校验和
	DownloadUrl   string                 `protobuf:"bytes,6,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`    // 可选：下载链接（对于大文件）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PluginResponse) Reset() {
	*x = PluginResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginResponse) ProtoMessage() {}

func (x *PluginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginResponse.ProtoReflect.Descriptor instead.
func (*PluginResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{11}
}

func (x *PluginResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *PluginResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PluginResponse) GetMetadata() *PluginMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *PluginResponse) GetPluginBinary() []byte {
	if x != nil {
		return x.PluginBinary
	}
	return nil
}

func (x *PluginResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *PluginResponse) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

// PluginMetadata 插件元数据
type PluginMetadata struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	Name                   string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Version                string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Description            string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	SupportedDeviceTypes   []string               `protobuf:"bytes,4,rep,name=supported_device_types,json=supportedDeviceTypes,proto3" json:"supported_device_types,omitempty"`
	SupportedArchitectures []string               `protobuf:"bytes,5,rep,name=supported_architectures,json=supportedArchitectures,proto3" json:"supported_architectures,omitempty"`
	SupportedOs            []string               `protobuf:"bytes,6,rep,name=supported_os,json=supportedOs,proto3" json:"supported_os,omitempty"`
	Configuration          map[string]string      `protobuf:"bytes,7,rep,name=configuration,proto3" json:"configuration,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Dependencies           []string               `protobuf:"bytes,8,rep,name=dependencies,proto3" json:"dependencies,omitempty"`
	SizeBytes              int64                  `protobuf:"varint,9,opt,name=size_bytes,json=sizeBytes,proto3" json:"size_bytes,omitempty"`
	Author                 string                 `protobuf:"bytes,10,opt,name=author,proto3" json:"author,omitempty"`
	CreatedAt              int64                  `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt              int64                  `protobuf:"varint,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *PluginMetadata) Reset() {
	*x = PluginMetadata{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginMetadata) ProtoMessage() {}

func (x *PluginMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginMetadata.ProtoReflect.Descriptor instead.
func (*PluginMetadata) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{12}
}

func (x *PluginMetadata) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PluginMetadata) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *PluginMetadata) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PluginMetadata) GetSupportedDeviceTypes() []string {
	if x != nil {
		return x.SupportedDeviceTypes
	}
	return nil
}

func (x *PluginMetadata) GetSupportedArchitectures() []string {
	if x != nil {
		return x.SupportedArchitectures
	}
	return nil
}

func (x *PluginMetadata) GetSupportedOs() []string {
	if x != nil {
		return x.SupportedOs
	}
	return nil
}

func (x *PluginMetadata) GetConfiguration() map[string]string {
	if x != nil {
		return x.Configuration
	}
	return nil
}

func (x *PluginMetadata) GetDependencies() []string {
	if x != nil {
		return x.Dependencies
	}
	return nil
}

func (x *PluginMetadata) GetSizeBytes() int64 {
	if x != nil {
		return x.SizeBytes
	}
	return 0
}

func (x *PluginMetadata) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *PluginMetadata) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PluginMetadata) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// PluginUpdateNotification 插件更新通知
type PluginUpdateNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgentId       string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	PluginName    string                 `protobuf:"bytes,2,opt,name=plugin_name,json=pluginName,proto3" json:"plugin_name,omitempty"`
	NewVersion    string                 `protobuf:"bytes,3,opt,name=new_version,json=newVersion,proto3" json:"new_version,omitempty"`
	OldVersion    string                 `protobuf:"bytes,4,opt,name=old_version,json=oldVersion,proto3" json:"old_version,omitempty"`
	IsMandatory   bool                   `protobuf:"varint,5,opt,name=is_mandatory,json=isMandatory,proto3" json:"is_mandatory,omitempty"` // 是否强制更新
	Reason        string                 `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`                               // 更新原因
	Metadata      *PluginMetadata        `protobuf:"bytes,7,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PluginUpdateNotification) Reset() {
	*x = PluginUpdateNotification{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginUpdateNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginUpdateNotification) ProtoMessage() {}

func (x *PluginUpdateNotification) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginUpdateNotification.ProtoReflect.Descriptor instead.
func (*PluginUpdateNotification) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{13}
}

func (x *PluginUpdateNotification) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *PluginUpdateNotification) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *PluginUpdateNotification) GetNewVersion() string {
	if x != nil {
		return x.NewVersion
	}
	return ""
}

func (x *PluginUpdateNotification) GetOldVersion() string {
	if x != nil {
		return x.OldVersion
	}
	return ""
}

func (x *PluginUpdateNotification) GetIsMandatory() bool {
	if x != nil {
		return x.IsMandatory
	}
	return false
}

func (x *PluginUpdateNotification) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *PluginUpdateNotification) GetMetadata() *PluginMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// PluginUpdateResponse 插件更新响应
type PluginUpdateResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Success           bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message           string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	NotificationCount int32                  `protobuf:"varint,3,opt,name=notification_count,json=notificationCount,proto3" json:"notification_count,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PluginUpdateResponse) Reset() {
	*x = PluginUpdateResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginUpdateResponse) ProtoMessage() {}

func (x *PluginUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginUpdateResponse.ProtoReflect.Descriptor instead.
func (*PluginUpdateResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{14}
}

func (x *PluginUpdateResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *PluginUpdateResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PluginUpdateResponse) GetNotificationCount() int32 {
	if x != nil {
		return x.NotificationCount
	}
	return 0
}

// PluginStatusReport Agent 上报插件状态
type PluginStatusReport struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	AgentId        string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	PluginStatuses []*PluginStatus        `protobuf:"bytes,2,rep,name=plugin_statuses,json=pluginStatuses,proto3" json:"plugin_statuses,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PluginStatusReport) Reset() {
	*x = PluginStatusReport{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginStatusReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginStatusReport) ProtoMessage() {}

func (x *PluginStatusReport) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginStatusReport.ProtoReflect.Descriptor instead.
func (*PluginStatusReport) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{15}
}

func (x *PluginStatusReport) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *PluginStatusReport) GetPluginStatuses() []*PluginStatus {
	if x != nil {
		return x.PluginStatuses
	}
	return nil
}

// PluginStatus 单个插件状态
type PluginStatus struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	PluginName        string                 `protobuf:"bytes,1,opt,name=plugin_name,json=pluginName,proto3" json:"plugin_name,omitempty"`
	Version           string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Status            string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"` // loaded, running, failed, unloaded
	ErrorMessage      string                 `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	LastUsedTimestamp int64                  `protobuf:"varint,5,opt,name=last_used_timestamp,json=lastUsedTimestamp,proto3" json:"last_used_timestamp,omitempty"`
	LoadTimestamp     int64                  `protobuf:"varint,6,opt,name=load_timestamp,json=loadTimestamp,proto3" json:"load_timestamp,omitempty"`
	Metrics           map[string]string      `protobuf:"bytes,7,rep,name=metrics,proto3" json:"metrics,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 插件相关指标
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PluginStatus) Reset() {
	*x = PluginStatus{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginStatus) ProtoMessage() {}

func (x *PluginStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginStatus.ProtoReflect.Descriptor instead.
func (*PluginStatus) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{16}
}

func (x *PluginStatus) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *PluginStatus) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *PluginStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PluginStatus) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *PluginStatus) GetLastUsedTimestamp() int64 {
	if x != nil {
		return x.LastUsedTimestamp
	}
	return 0
}

func (x *PluginStatus) GetLoadTimestamp() int64 {
	if x != nil {
		return x.LoadTimestamp
	}
	return 0
}

func (x *PluginStatus) GetMetrics() map[string]string {
	if x != nil {
		return x.Metrics
	}
	return nil
}

// PluginStatusResponse 插件状态上报响应
type PluginStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Actions       []string               `protobuf:"bytes,3,rep,name=actions,proto3" json:"actions,omitempty"` // 建议的操作：reload, unload, update, etc.
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PluginStatusResponse) Reset() {
	*x = PluginStatusResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginStatusResponse) ProtoMessage() {}

func (x *PluginStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginStatusResponse.ProtoReflect.Descriptor instead.
func (*PluginStatusResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{17}
}

func (x *PluginStatusResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *PluginStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PluginStatusResponse) GetActions() []string {
	if x != nil {
		return x.Actions
	}
	return nil
}

var File_pkg_proto_devinsight_proto protoreflect.FileDescriptor

const file_pkg_proto_devinsight_proto_rawDesc = "" +
	"\n" +
	"\x1apkg/proto/devinsight.proto\x12\n" +
	"devinsight\x1a\x1bgoogle/protobuf/empty.proto\"\xc7\x01\n" +
	"\x14RegisterAgentRequest\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12\x19\n" +
	"\bagent_ip\x18\x02 \x01(\tR\aagentIp\x12:\n" +
	"\x19supported_collector_types\x18\x03 \x03(\tR\x17supportedCollectorTypes\x12=\n" +
	"\rdevice_config\x18\x04 \x01(\v2\x18.devinsight.DeviceConfigR\fdeviceConfig\"\xbd\x02\n" +
	"\fDeviceConfig\x12\"\n" +
	"\rmax_memory_mb\x18\x01 \x01(\x03R\vmaxMemoryMb\x12&\n" +
	"\x0fmax_cpu_percent\x18\x02 \x01(\x05R\rmaxCpuPercent\x12\x1e\n" +
	"\vmax_disk_mb\x18\x03 \x01(\x03R\tmaxDiskMb\x120\n" +
	"\x14max_concurrent_tasks\x18\x04 \x01(\x05R\x12maxConcurrentTasks\x12N\n" +
	"\fcapabilities\x18\x05 \x03(\v2*.devinsight.DeviceConfig.CapabilitiesEntryR\fcapabilities\x1a?\n" +
	"\x11CapabilitiesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"K\n" +
	"\x15RegisterAgentResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xfb\x03\n" +
	"\x13CollectorTaskConfig\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\x12\x1f\n" +
	"\vdevice_name\x18\x03 \x01(\tR\n" +
	"deviceName\x12\x1f\n" +
	"\vdevice_type\x18\x04 \x01(\tR\n" +
	"deviceType\x12\x12\n" +
	"\x04host\x18\x05 \x01(\tR\x04host\x12\x12\n" +
	"\x04port\x18\x06 \x01(\x05R\x04port\x12\x1a\n" +
	"\busername\x18\a \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\b \x01(\tR\bpassword\x12Y\n" +
	"\x0econnect_params\x18\t \x03(\v22.devinsight.CollectorTaskConfig.ConnectParamsEntryR\rconnectParams\x12+\n" +
	"\x11frequency_seconds\x18\n" +
	" \x01(\x03R\x10frequencySeconds\x12#\n" +
	"\rcollect_items\x18\v \x03(\tR\fcollectItems\x12\x1d\n" +
	"\n" +
	"is_enabled\x18\f \x01(\bR\tisEnabled\x1a@\n" +
	"\x12ConnectParamsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x98\x01\n" +
	"\n" +
	"TaskStatus\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12#\n" +
	"\rerror_message\x18\x03 \x01(\tR\ferrorMessage\x124\n" +
	"\x16last_collect_timestamp\x18\x04 \x01(\x03R\x14lastCollectTimestamp\"\xfb\x02\n" +
	"\n" +
	"MetricData\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x1d\n" +
	"\n" +
	"metric_key\x18\x02 \x01(\tR\tmetricKey\x12%\n" +
	"\rnumeric_value\x18\x03 \x01(\x01H\x00R\fnumericValue\x12#\n" +
	"\fstring_value\x18\x04 \x01(\tH\x00R\vstringValue\x12%\n" +
	"\rboolean_value\x18\x05 \x01(\bH\x00R\fbooleanValue\x12\x1c\n" +
	"\ttimestamp\x18\x06 \x01(\x03R\ttimestamp\x12\x1b\n" +
	"\tjson_data\x18\a \x01(\tR\bjsonData\x12:\n" +
	"\x06labels\x18\b \x03(\v2\".devinsight.MetricData.LabelsEntryR\x06labels\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B\f\n" +
	"\n" +
	"value_type\"\xec\x02\n" +
	"\x0fSupportedMetric\x12\x1d\n" +
	"\n" +
	"metric_key\x18\x01 \x01(\tR\tmetricKey\x12\x1f\n" +
	"\vmetric_name\x18\x02 \x01(\tR\n" +
	"metricName\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1b\n" +
	"\tdata_type\x18\x04 \x01(\tR\bdataType\x12\x12\n" +
	"\x04unit\x18\x05 \x01(\tR\x04unit\x12E\n" +
	"\bmetadata\x18\x06 \x03(\v2).devinsight.SupportedMetric.MetadataEntryR\bmetadata\x12\x1b\n" +
	"\tis_active\x18\a \x01(\bR\bisActive\x12%\n" +
	"\x0ecollector_type\x18\b \x01(\tR\rcollectorType\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x89\x02\n" +
	"\bLogEntry\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x1b\n" +
	"\tlog_level\x18\x02 \x01(\tR\blogLevel\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\x12\x1c\n" +
	"\ttimestamp\x18\x04 \x01(\x03R\ttimestamp\x12\x16\n" +
	"\x06source\x18\x05 \x01(\tR\x06source\x128\n" +
	"\x06fields\x18\x06 \x03(\v2 .devinsight.LogEntry.FieldsEntryR\x06fields\x1a9\n" +
	"\vFieldsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"r\n" +
	"\x15StreamLogDataResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12%\n" +
	"\x0ereceived_count\x18\x03 \x01(\x05R\rreceivedCount\"u\n" +
	"\x18StreamMetricDataResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12%\n" +
	"\x0ereceived_count\x18\x03 \x01(\x05R\rreceivedCount\"\xc7\x01\n" +
	"\rPluginRequest\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12\x1f\n" +
	"\vplugin_name\x18\x02 \x01(\tR\n" +
	"pluginName\x12%\n" +
	"\x0eplugin_version\x18\x03 \x01(\tR\rpluginVersion\x12\x1f\n" +
	"\vdevice_type\x18\x04 \x01(\tR\n" +
	"deviceType\x12\"\n" +
	"\farchitecture\x18\x05 \x01(\tR\farchitecture\x12\x0e\n" +
	"\x02os\x18\x06 \x01(\tR\x02os\"\xe0\x01\n" +
	"\x0ePluginResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x126\n" +
	"\bmetadata\x18\x03 \x01(\v2\x1a.devinsight.PluginMetadataR\bmetadata\x12#\n" +
	"\rplugin_binary\x18\x04 \x01(\fR\fpluginBinary\x12\x1a\n" +
	"\bchecksum\x18\x05 \x01(\tR\bchecksum\x12!\n" +
	"\fdownload_url\x18\x06 \x01(\tR\vdownloadUrl\"\xa2\x04\n" +
	"\x0ePluginMetadata\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x124\n" +
	"\x16supported_device_types\x18\x04 \x03(\tR\x14supportedDeviceTypes\x127\n" +
	"\x17supported_architectures\x18\x05 \x03(\tR\x16supportedArchitectures\x12!\n" +
	"\fsupported_os\x18\x06 \x03(\tR\vsupportedOs\x12S\n" +
	"\rconfiguration\x18\a \x03(\v2-.devinsight.PluginMetadata.ConfigurationEntryR\rconfiguration\x12\"\n" +
	"\fdependencies\x18\b \x03(\tR\fdependencies\x12\x1d\n" +
	"\n" +
	"size_bytes\x18\t \x01(\x03R\tsizeBytes\x12\x16\n" +
	"\x06author\x18\n" +
	" \x01(\tR\x06author\x12\x1d\n" +
	"\n" +
	"created_at\x18\v \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\f \x01(\x03R\tupdatedAt\x1a@\n" +
	"\x12ConfigurationEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x8b\x02\n" +
	"\x18PluginUpdateNotification\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12\x1f\n" +
	"\vplugin_name\x18\x02 \x01(\tR\n" +
	"pluginName\x12\x1f\n" +
	"\vnew_version\x18\x03 \x01(\tR\n" +
	"newVersion\x12\x1f\n" +
	"\vold_version\x18\x04 \x01(\tR\n" +
	"oldVersion\x12!\n" +
	"\fis_mandatory\x18\x05 \x01(\bR\visMandatory\x12\x16\n" +
	"\x06reason\x18\x06 \x01(\tR\x06reason\x126\n" +
	"\bmetadata\x18\a \x01(\v2\x1a.devinsight.PluginMetadataR\bmetadata\"y\n" +
	"\x14PluginUpdateResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12-\n" +
	"\x12notification_count\x18\x03 \x01(\x05R\x11notificationCount\"r\n" +
	"\x12PluginStatusReport\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12A\n" +
	"\x0fplugin_statuses\x18\x02 \x03(\v2\x18.devinsight.PluginStatusR\x0epluginStatuses\"\xda\x02\n" +
	"\fPluginStatus\x12\x1f\n" +
	"\vplugin_name\x18\x01 \x01(\tR\n" +
	"pluginName\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x12#\n" +
	"\rerror_message\x18\x04 \x01(\tR\ferrorMessage\x12.\n" +
	"\x13last_used_timestamp\x18\x05 \x01(\x03R\x11lastUsedTimestamp\x12%\n" +
	"\x0eload_timestamp\x18\x06 \x01(\x03R\rloadTimestamp\x12?\n" +
	"\ametrics\x18\a \x03(\v2%.devinsight.PluginStatus.MetricsEntryR\ametrics\x1a:\n" +
	"\fMetricsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"d\n" +
	"\x14PluginStatusResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x18\n" +
	"\aactions\x18\x03 \x03(\tR\aactions2\xde\x04\n" +
	"\fAgentService\x12V\n" +
	"\rRegisterAgent\x12 .devinsight.RegisterAgentRequest\x1a!.devinsight.RegisterAgentResponse\"\x00\x12U\n" +
	"\x14StreamCollectorTasks\x12\x16.devinsight.TaskStatus\x1a\x1f.devinsight.CollectorTaskConfig\"\x00(\x010\x01\x12T\n" +
	"\x10StreamMetricData\x12\x16.devinsight.MetricData\x1a$.devinsight.StreamMetricDataResponse\"\x00(\x01\x12L\n" +
	"\rStreamLogData\x12\x14.devinsight.LogEntry\x1a!.devinsight.StreamLogDataResponse\"\x00(\x01\x12H\n" +
	"\rRequestPlugin\x12\x19.devinsight.PluginRequest\x1a\x1a.devinsight.PluginResponse\"\x00\x12W\n" +
	"\x13StreamPluginUpdates\x12\x16.google.protobuf.Empty\x1a$.devinsight.PluginUpdateNotification\"\x000\x01\x12X\n" +
	"\x12ReportPluginStatus\x12\x1e.devinsight.PluginStatusReport\x1a .devinsight.PluginStatusResponse\"\x00B\x11Z\x0faiops/pkg/protob\x06proto3"

var (
	file_pkg_proto_devinsight_proto_rawDescOnce sync.Once
	file_pkg_proto_devinsight_proto_rawDescData []byte
)

func file_pkg_proto_devinsight_proto_rawDescGZIP() []byte {
	file_pkg_proto_devinsight_proto_rawDescOnce.Do(func() {
		file_pkg_proto_devinsight_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pkg_proto_devinsight_proto_rawDesc), len(file_pkg_proto_devinsight_proto_rawDesc)))
	})
	return file_pkg_proto_devinsight_proto_rawDescData
}

var file_pkg_proto_devinsight_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_pkg_proto_devinsight_proto_goTypes = []any{
	(*RegisterAgentRequest)(nil),     // 0: devinsight.RegisterAgentRequest
	(*DeviceConfig)(nil),             // 1: devinsight.DeviceConfig
	(*RegisterAgentResponse)(nil),    // 2: devinsight.RegisterAgentResponse
	(*CollectorTaskConfig)(nil),      // 3: devinsight.CollectorTaskConfig
	(*TaskStatus)(nil),               // 4: devinsight.TaskStatus
	(*MetricData)(nil),               // 5: devinsight.MetricData
	(*SupportedMetric)(nil),          // 6: devinsight.SupportedMetric
	(*LogEntry)(nil),                 // 7: devinsight.LogEntry
	(*StreamLogDataResponse)(nil),    // 8: devinsight.StreamLogDataResponse
	(*StreamMetricDataResponse)(nil), // 9: devinsight.StreamMetricDataResponse
	(*PluginRequest)(nil),            // 10: devinsight.PluginRequest
	(*PluginResponse)(nil),           // 11: devinsight.PluginResponse
	(*PluginMetadata)(nil),           // 12: devinsight.PluginMetadata
	(*PluginUpdateNotification)(nil), // 13: devinsight.PluginUpdateNotification
	(*PluginUpdateResponse)(nil),     // 14: devinsight.PluginUpdateResponse
	(*PluginStatusReport)(nil),       // 15: devinsight.PluginStatusReport
	(*PluginStatus)(nil),             // 16: devinsight.PluginStatus
	(*PluginStatusResponse)(nil),     // 17: devinsight.PluginStatusResponse
	nil,                              // 18: devinsight.DeviceConfig.CapabilitiesEntry
	nil,                              // 19: devinsight.CollectorTaskConfig.ConnectParamsEntry
	nil,                              // 20: devinsight.MetricData.LabelsEntry
	nil,                              // 21: devinsight.SupportedMetric.MetadataEntry
	nil,                              // 22: devinsight.LogEntry.FieldsEntry
	nil,                              // 23: devinsight.PluginMetadata.ConfigurationEntry
	nil,                              // 24: devinsight.PluginStatus.MetricsEntry
	(*emptypb.Empty)(nil),            // 25: google.protobuf.Empty
}
var file_pkg_proto_devinsight_proto_depIdxs = []int32{
	1,  // 0: devinsight.RegisterAgentRequest.device_config:type_name -> devinsight.DeviceConfig
	18, // 1: devinsight.DeviceConfig.capabilities:type_name -> devinsight.DeviceConfig.CapabilitiesEntry
	19, // 2: devinsight.CollectorTaskConfig.connect_params:type_name -> devinsight.CollectorTaskConfig.ConnectParamsEntry
	20, // 3: devinsight.MetricData.labels:type_name -> devinsight.MetricData.LabelsEntry
	21, // 4: devinsight.SupportedMetric.metadata:type_name -> devinsight.SupportedMetric.MetadataEntry
	22, // 5: devinsight.LogEntry.fields:type_name -> devinsight.LogEntry.FieldsEntry
	12, // 6: devinsight.PluginResponse.metadata:type_name -> devinsight.PluginMetadata
	23, // 7: devinsight.PluginMetadata.configuration:type_name -> devinsight.PluginMetadata.ConfigurationEntry
	12, // 8: devinsight.PluginUpdateNotification.metadata:type_name -> devinsight.PluginMetadata
	16, // 9: devinsight.PluginStatusReport.plugin_statuses:type_name -> devinsight.PluginStatus
	24, // 10: devinsight.PluginStatus.metrics:type_name -> devinsight.PluginStatus.MetricsEntry
	0,  // 11: devinsight.AgentService.RegisterAgent:input_type -> devinsight.RegisterAgentRequest
	4,  // 12: devinsight.AgentService.StreamCollectorTasks:input_type -> devinsight.TaskStatus
	5,  // 13: devinsight.AgentService.StreamMetricData:input_type -> devinsight.MetricData
	7,  // 14: devinsight.AgentService.StreamLogData:input_type -> devinsight.LogEntry
	10, // 15: devinsight.AgentService.RequestPlugin:input_type -> devinsight.PluginRequest
	25, // 16: devinsight.AgentService.StreamPluginUpdates:input_type -> google.protobuf.Empty
	15, // 17: devinsight.AgentService.ReportPluginStatus:input_type -> devinsight.PluginStatusReport
	2,  // 18: devinsight.AgentService.RegisterAgent:output_type -> devinsight.RegisterAgentResponse
	3,  // 19: devinsight.AgentService.StreamCollectorTasks:output_type -> devinsight.CollectorTaskConfig
	9,  // 20: devinsight.AgentService.StreamMetricData:output_type -> devinsight.StreamMetricDataResponse
	8,  // 21: devinsight.AgentService.StreamLogData:output_type -> devinsight.StreamLogDataResponse
	11, // 22: devinsight.AgentService.RequestPlugin:output_type -> devinsight.PluginResponse
	13, // 23: devinsight.AgentService.StreamPluginUpdates:output_type -> devinsight.PluginUpdateNotification
	17, // 24: devinsight.AgentService.ReportPluginStatus:output_type -> devinsight.PluginStatusResponse
	18, // [18:25] is the sub-list for method output_type
	11, // [11:18] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_pkg_proto_devinsight_proto_init() }
func file_pkg_proto_devinsight_proto_init() {
	if File_pkg_proto_devinsight_proto != nil {
		return
	}
	file_pkg_proto_devinsight_proto_msgTypes[5].OneofWrappers = []any{
		(*MetricData_NumericValue)(nil),
		(*MetricData_StringValue)(nil),
		(*MetricData_BooleanValue)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pkg_proto_devinsight_proto_rawDesc), len(file_pkg_proto_devinsight_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pkg_proto_devinsight_proto_goTypes,
		DependencyIndexes: file_pkg_proto_devinsight_proto_depIdxs,
		MessageInfos:      file_pkg_proto_devinsight_proto_msgTypes,
	}.Build()
	File_pkg_proto_devinsight_proto = out.File
	file_pkg_proto_devinsight_proto_goTypes = nil
	file_pkg_proto_devinsight_proto_depIdxs = nil
}
