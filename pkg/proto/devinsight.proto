syntax = "proto3";

import "google/protobuf/empty.proto";

package devinsight;

option go_package = "aiops/pkg/proto";

// AgentService 定义了 Agent 和 Control Plane 之间的通信接口
service AgentService {
    // RegisterAgent 用于 Agent 向 Control Plane 注册和发送心跳
    rpc RegisterAgent(RegisterAgentRequest) returns (RegisterAgentResponse) {}
    
    // StreamCollectorTasks 是双向流，用于下发采集任务配置和接收任务状态
    rpc StreamCollectorTasks(stream TaskStatus) returns (stream CollectorTaskConfig) {}
    
    // StreamMetricData 是单向流，用于 Agent 上报采集到的指标数据
    rpc StreamMetricData(stream MetricData) returns (StreamMetricDataResponse) {}
    
    // StreamLogData 是单向流，用于 Agent 上报日志数据
    rpc StreamLogData(stream LogEntry) returns (StreamLogDataResponse) {}
    
    // RequestPlugin 用于 Agent 请求特定的插件
    rpc RequestPlugin(PluginRequest) returns (PluginResponse) {}
    
    // StreamPluginUpdates 是服务器流，用于 Control Plane 推送插件更新通知
    rpc StreamPluginUpdates(google.protobuf.Empty) returns (stream PluginUpdateNotification) {}
    
    // ReportPluginStatus 用于 Agent 上报插件状态
    rpc ReportPluginStatus(PluginStatusReport) returns (PluginStatusResponse) {}
}

// RegisterAgentRequest Agent 注册请求
message RegisterAgentRequest {
    string agent_id = 1;
    string agent_ip = 2;
    repeated string supported_collector_types = 3;
    DeviceConfig device_config = 4; // Agent设备配置信息
}

// DeviceConfig Agent设备配置信息
message DeviceConfig {
    int64 max_memory_mb = 1;
    int32 max_cpu_percent = 2;
    int64 max_disk_mb = 3;
    int32 max_concurrent_tasks = 4;
    map<string, string> capabilities = 5;
}

// RegisterAgentResponse Agent 注册响应
message RegisterAgentResponse {
    bool success = 1;
    string message = 2;
}

// CollectorTaskConfig 采集任务配置
message CollectorTaskConfig {
    string task_id = 1;
    string device_id = 2;
    string device_name = 3;
    string device_type = 4;
    string host = 5;
    int32 port = 6;
    string username = 7;
    string password = 8;
    map<string, string> connect_params = 9;
    int64 frequency_seconds = 10;
    repeated string collect_items = 11;
    bool is_enabled = 12;
}

// TaskStatus 任务状态上报
message TaskStatus {
    string task_id = 1;
    string status = 2; // running, failed, success
    string error_message = 3;
    int64 last_collect_timestamp = 4;
}

// MetricData 指标数据点 - 支持灵活的多维数据
message MetricData {
    string device_id = 1;
    string metric_key = 2;
    oneof value_type {
        double numeric_value = 3;
        string string_value = 4;
        bool boolean_value = 5;
    }
    int64 timestamp = 6;
    string json_data = 7; // 灵活的JSON格式数据，支持复杂的多维指标
    map<string, string> labels = 8;
}

// SupportedMetric 支持的指标定义
message SupportedMetric {
    string metric_key = 1;
    string metric_name = 2;
    string description = 3;
    string data_type = 4; // numeric, string, boolean, json
    string unit = 5;
    map<string, string> metadata = 6;
    bool is_active = 7;
    string collector_type = 8;
}

// LogEntry 日志条目
message LogEntry {
    string device_id = 1;
    string log_level = 2; // DEBUG, INFO, WARN, ERROR
    string message = 3;
    int64 timestamp = 4;
    string source = 5; // 日志来源：agent, collector, etc.
    map<string, string> fields = 6; // 额外的结构化字段
}

// StreamLogDataResponse 日志数据上报响应
message StreamLogDataResponse {
    bool success = 1;
    string message = 2;
    int32 received_count = 3;
}

// StreamMetricDataResponse 指标数据上报响应
message StreamMetricDataResponse {
    bool success = 1;
    string message = 2;
    int32 received_count = 3;
}

// Plugin Distribution Messages

// PluginRequest Agent 请求插件
message PluginRequest {
    string agent_id = 1;
    string plugin_name = 2;
    string plugin_version = 3; // 可选，如果为空则获取最新版本
    string device_type = 4; // 设备类型，用于确定插件兼容性
    string architecture = 5; // 系统架构：amd64, arm64, etc.
    string os = 6; // 操作系统：linux, windows, darwin
}

// PluginResponse Control Plane 响应插件请求
message PluginResponse {
    bool success = 1;
    string message = 2;
    PluginMetadata metadata = 3;
    bytes plugin_binary = 4; // 插件二进制文件
    string checksum = 5; // 插件文件校验和
    string download_url = 6; // 可选：下载链接（对于大文件）
}

// PluginMetadata 插件元数据
message PluginMetadata {
    string name = 1;
    string version = 2;
    string description = 3;
    repeated string supported_device_types = 4;
    repeated string supported_architectures = 5;
    repeated string supported_os = 6;
    map<string, string> configuration = 7;
    repeated string dependencies = 8;
    int64 size_bytes = 9;
    string author = 10;
    int64 created_at = 11;
    int64 updated_at = 12;
}

// PluginUpdateNotification 插件更新通知
message PluginUpdateNotification {
    string agent_id = 1;
    string plugin_name = 2;
    string new_version = 3;
    string old_version = 4;
    bool is_mandatory = 5; // 是否强制更新
    string reason = 6; // 更新原因
    PluginMetadata metadata = 7;
}

// PluginUpdateResponse 插件更新响应
message PluginUpdateResponse {
    bool success = 1;
    string message = 2;
    int32 notification_count = 3;
}

// PluginStatusReport Agent 上报插件状态
message PluginStatusReport {
    string agent_id = 1;
    repeated PluginStatus plugin_statuses = 2;
}

// PluginStatus 单个插件状态
message PluginStatus {
    string plugin_name = 1;
    string version = 2;
    string status = 3; // loaded, running, failed, unloaded
    string error_message = 4;
    int64 last_used_timestamp = 5;
    int64 load_timestamp = 6;
    map<string, string> metrics = 7; // 插件相关指标
}

// PluginStatusResponse 插件状态上报响应
message PluginStatusResponse {
    bool success = 1;
    string message = 2;
    repeated string actions = 3; // 建议的操作：reload, unload, update, etc.
}
