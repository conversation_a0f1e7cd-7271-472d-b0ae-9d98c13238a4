2024-05-24 10:00:01 [INFO] Agent 启动中...
2024-05-24 10:00:02 [DEBUG] 连接到控制平面
2024-05-24 10:00:03 [WARN] 配置项缺失，使用默认值
2024-05-24 10:00:04 [INFO] Agent 注册成功
2024-05-24 10:00:05 [ERROR] 某个错误信息用于测试
2025-05-24 23:35:58.256261000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 232, "dropped": 0}
2025-05-24 23:36:00.415161000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-24 23:36:00.415612000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "test-agent-001"}
2025-05-24 23:36:00.415632000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-24 23:36:00.415698000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-24 23:36:00.420656000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-24 23:36:00.420693000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-24 23:36:00.422463000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-24 23:36:00.422507000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-24 23:36:00.415698000"}
2025-05-24 23:36:00.422525000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-24 23:36:00.420655000"}
2025-05-24 23:36:00.422641000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-24 23:36:00.422592000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-24 23:36:00.422647000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-24 23:36:00.422679000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-24 23:36:00.422769000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-24 23:36:00.422844000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-24 23:36:00.422871000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-24 23:36:00.422906000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-24 23:36:00.422944000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-24 23:36:00.422952000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-24 23:36:08.258173000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 244, "dropped": 0}
2025-05-24 23:36:10.425322000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2024-05-24 10:01:01 [INFO] 新的日志条目 - 测试日志转发
2024-05-24 10:01:02 [ERROR] 测试错误日志转发
2025-05-24 23:36:18.260515000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 246, "dropped": 0}
2025-05-24 23:36:20.426724000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 6, "dropped": 0}
2024-05-24 10:01:01 [INFO] 批量日志测试 - 条目 1
2024-05-24 10:02:01 [INFO] 批量日志测试 - 条目 2
2024-05-24 10:03:01 [INFO] 批量日志测试 - 条目 3
2024-05-24 10:04:01 [INFO] 批量日志测试 - 条目 4
2024-05-24 10:05:01 [INFO] 批量日志测试 - 条目 5
2025-05-24 23:36:44.982878000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:176	接收到退出信号，正在关闭 Agent...
2025-05-24 23:36:44.983173000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:338	指标流上下文取消，正在关闭...
2025-05-24 23:36:44.983190000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:398	日志流上下文取消，正在关闭...
2025-05-24 23:36:44.983247000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:284	任务流正在关闭...
2025-05-24 23:36:44.983277000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:286	所有采集器已停止.
2025-05-24 23:36:44.983269000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:251	停止接收任务配置，流已关闭或上下文已取消:	{"error": "rpc error: code = Canceled desc = context canceled"}
2025-05-24 23:36:48.266549000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 308, "dropped": 0}
2025-05-24 23:36:50.432560000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 68, "dropped": 0}
2025-05-24 23:37:00.420907000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:225	健康检查失败，开始重连	{"error": "连接状态异常: IDLE"}
2025-05-24 23:37:00.421172000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connected", "new_state": "Reconnecting"}
2025-05-24 23:37:00.421202000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Reconnecting", "timestamp": "2025-05-24 23:37:00.421163000"}
2025-05-24 23:37:00.421476000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 1, "server": "localhost:50051"}
2025-05-24 23:37:10.422579000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 1}
2025-05-24 23:37:10.438885000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 69, "dropped": 0}
2025-05-24 23:37:11.429575000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 2, "server": "localhost:50051"}
2025-05-24 23:37:18.272763000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 309, "dropped": 0}
2025-05-24 23:37:21.430952000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 2}
2025-05-24 23:37:23.432188000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 3, "server": "localhost:50051"}
2025-05-24 23:37:33.433492000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 4}
2025-05-24 23:37:37.434686000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 4, "server": "localhost:50051"}
2025-05-24 23:37:47.436023000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 8}
2025-05-24 23:37:55.437245000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 5, "server": "localhost:50051"}
2025-05-24 23:38:05.440263000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 16}
2025-05-24 23:38:20.452307000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 83, "dropped": 0}
2025-05-24 23:38:21.542817000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 6, "server": "localhost:50051"}
2025-05-24 23:38:21.545298000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Reconnecting", "new_state": "Connected"}
2025-05-24 23:38:21.545406000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:337	重连成功	{"server": "localhost:50051"}
2025-05-24 23:38:21.545419000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-24 23:38:21.545287000"}
2025-05-24 23:38:28.285562000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 323, "dropped": 0}
2025-05-24 23:39:00.564825000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 88, "dropped": 0}
2025-05-24 23:39:08.293980000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 328, "dropped": 0}
2025-05-24 23:39:18.295579000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 334, "dropped": 0}
2025-05-24 23:39:20.569483000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 94, "dropped": 0}
2025-05-24 23:39:21.545156000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:225	健康检查失败，开始重连	{"error": "连接状态异常: IDLE"}
2025-05-24 23:39:21.545363000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Reconnecting", "timestamp": "2025-05-24 23:39:21.545348000"}
2025-05-24 23:39:21.545386000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connected", "new_state": "Reconnecting"}
2025-05-24 23:39:21.545738000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 1, "server": "localhost:50051"}
2025-05-24 23:39:31.546851000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 1}
2025-05-24 23:39:32.548039000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 2, "server": "localhost:50051"}
2025-05-24 23:39:42.549335000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 2}
2025-05-24 23:39:44.550465000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 3, "server": "localhost:50051"}
2025-05-24 23:39:54.551739000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 4}
2025-05-24 23:39:58.552892000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 4, "server": "localhost:50051"}
2025-05-24 23:40:08.573953000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 8}
2025-05-24 23:40:16.678695000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 5, "server": "localhost:50051"}
2025-05-24 23:40:26.679838000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 16}
2025-05-24 23:40:38.241451000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 15, "server": "localhost:50051"}
2025-05-24 23:40:42.779901000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 6, "server": "localhost:50051"}
2025-05-24 23:40:48.246080000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-24 23:40:52.782891000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 32}
2025-05-24 23:41:24.885549000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 7, "server": "localhost:50051"}
2025-05-24 23:41:35.244038000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 64}
2025-05-24 23:42:39.405710000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 8, "server": "localhost:50051"}
2025-05-24 23:42:50.094940000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 128}
2025-05-24 23:43:00.662694000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 100, "dropped": 0}
2025-05-24 23:43:08.488068000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 340, "dropped": 0}
2025-05-24 23:44:58.320009000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 9, "server": "localhost:50051"}
2025-05-24 23:45:08.406784000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 256}
2025-05-24 23:45:48.299633000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 16, "server": "localhost:50051"}
2025-05-24 23:45:58.318982000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-24 23:47:20.732143000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 101, "dropped": 0}
2025-05-24 23:47:28.541193000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 341, "dropped": 0}
2025-05-24 23:48:58.674432000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 348, "dropped": 0}
2025-05-24 23:49:00.819960000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 108, "dropped": 0}
2025-05-24 23:49:24.519805000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 10, "server": "localhost:50051"}
2025-05-24 23:49:34.886957000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 300}
2025-05-24 23:50:58.322202000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 17, "server": "localhost:50051"}
2025-05-24 23:51:08.326791000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-24 23:53:40.881209000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 112, "dropped": 0}
2025-05-24 23:53:48.866660000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 352, "dropped": 0}
2025-05-24 23:54:34.888504000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 11, "server": "localhost:50051"}
2025-05-24 23:54:45.247194000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 300}
2025-05-24 23:56:08.326457000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 18, "server": "localhost:50051"}
2025-05-24 23:56:18.331025000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-24 23:57:01.198706000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 116, "dropped": 0}
2025-05-24 23:57:09.007067000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 356, "dropped": 0}
2025-05-24 23:57:59.031729000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 361, "dropped": 0}
2025-05-24 23:58:01.208057000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 121, "dropped": 0}
2025-05-24 23:59:31.227541000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 125, "dropped": 0}
2025-05-24 23:59:39.150356000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 365, "dropped": 0}
2025-05-24 23:59:45.328263000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 12, "server": "localhost:50051"}
2025-05-24 23:59:55.435835000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 300}
2025-05-25 00:00:00.543169000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:264	监控的日志文件已被删除	{"file": "/var/log/system.log"}
2025-05-25 00:00:00.543270000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:215	移除日志文件监控	{"file": "/var/log/system.log"}
2025-05-25 00:00:06.027396000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:264	监控的日志文件已被删除	{"file": "/var/log/system.log"}
2025-05-25 00:00:06.028081000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:215	移除日志文件监控	{"file": "/var/log/system.log"}
2025-05-25 00:01:18.411792000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 19, "server": "localhost:50051"}
2025-05-25 00:01:28.413588000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-25 00:04:55.434739000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 13, "server": "localhost:50051"}
2025-05-25 00:05:05.551816000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 300}
2025-05-25 00:06:28.412732000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 20, "server": "localhost:50051"}
2025-05-25 00:06:38.415146000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-25 14:52:47.367876000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 14:52:47.381882000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "5a5bcaaf-cd71-4673-b500-cfda3db896ea"}
2025-05-25 14:52:47.381907000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 14:52:47.381931000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:231	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 14:52:47.382058000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:47.483248000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:47.584721000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:47.685943000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:47.787290000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:47.888538000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:47.989774000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.090324000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.191551000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.292769000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.393989000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.495215000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.598398000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.698753000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.799928000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.901104000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.002326000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.103554000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.204798000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.306017000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.407158000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.508455000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.609671000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.710397000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.811736000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.912068000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.013279000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.114495000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.214859000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.316091000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.417333000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.518501000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.619704000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.720881000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.822044000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.923280000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.024523000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.125746000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.226581000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.327743000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.428967000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.530320000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.631693000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.732509000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.833890000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.935226000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.036594000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.137895000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.239253000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.340622000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.441910000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.543209000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.644409000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.745638000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.847005000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.948345000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.049675000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.150948000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.252055000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.353239000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.454574000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.555908000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.656309000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.757452000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.858502000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.959706000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.060910000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.162119000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.263337000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.364555000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.465372000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.566677000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.667982000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.768272000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.869475000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.970706000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.071918000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.173113000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.274319000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.375543000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.476738000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.577984000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.679299000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.780511000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.881712000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.982934000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.084171000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.185376000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.286590000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.387793000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.489014000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.590260000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.691613000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.792815000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.894026000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.995213000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:57.096420000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:57.197662000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:57.298883000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:57.382244000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:231	连接状态变更	{"old_state": "Connecting", "new_state": "Disconnected"}
2025-05-25 14:52:57.382359000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:67	连接控制平面失败:	{"error": "创建连接失败: 连接超时: context deadline exceeded"}
main.main
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:67
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-25 15:40:40.806677000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 15:40:40.821672000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "26088ec6-f8f4-4a40-81c9-db5c983dc423"}
2025-05-25 15:40:40.821712000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 15:40:40.821737000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:231	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 15:40:40.821885000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:40.922989000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.024161000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.125301000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.226548000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.327783000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.429059000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.530293000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.631556000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.732873000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.834079000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.935348000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.036642000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.137878000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.239109000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.340349000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.441552000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.542851000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.644097000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.745342000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.846544000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.947780000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.048980000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.150185000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.251392000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.352169000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.453393000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.554634000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.655860000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.757148000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.857848000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.959104000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.060376000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.161642000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.262932000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.364253000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.465582000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.566888000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.667276000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.768604000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.869918000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.971243000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.072609000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.174040000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.275418000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.376752000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.477972000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.579223000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.680522000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.781787000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.883165000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.984556000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.085903000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.187216000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.288586000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.389841000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.491071000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.592303000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.693541000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.794828000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.896149000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.997410000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.098654000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.199938000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.301234000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.402463000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.503707000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.604939000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.706169000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.807443000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.908874000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.010081000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.111296000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.212495000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.313669000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.414877000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.516093000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.617300000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.718528000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.819762000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.921149000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.022504000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.123830000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.225099000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.326376000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.427629000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.528885000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.630159000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.731434000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.832696000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.933836000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.033967000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.135087000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.236319000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.337420000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.437785000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.538953000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.640129000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.741364000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.823296000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:231	连接状态变更	{"old_state": "Connecting", "new_state": "Disconnected"}
2025-05-25 15:40:50.823545000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:67	连接控制平面失败:	{"error": "创建连接失败: 连接超时: context deadline exceeded"}
main.main
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:67
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-25 15:47:24.298132000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 15:47:24.395817000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "c1169546-c941-473b-84d8-2a7e18ac3a0f"}
2025-05-25 15:47:24.395866000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 15:47:24.395901000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:231	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 15:47:24.395996000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:24.497051000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:24.597857000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:24.699028000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:24.799819000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:24.901553000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.002833000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.104165000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.205502000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.306433000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.407462000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.508760000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.610222000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.711503000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.811860000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.912496000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.013653000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.115653000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.216819000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.318087000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.419517000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.520842000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.622104000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.723407000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.824189000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.925446000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.026924000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.128202000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.228921000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.330211000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.431536000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.532861000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.633437000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.734807000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.836121000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.937363000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.038700000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.140081000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.241397000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.342703000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.444030000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.545179000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.646367000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.747788000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.849092000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.950362000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.051683000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.152998000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.254309000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.355693000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.457036000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.558422000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.659740000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.761107000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.862429000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.963037000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.064341000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.165718000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.267048000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.368381000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.469683000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.571000000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.672312000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.773641000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.874957000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.976275000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.077647000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.178954000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.280281000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.381602000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.483000000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.584361000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.685681000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.786997000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.888377000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.989638000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.090969000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.192275000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.293481000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.436539000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.537693000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.638843000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.740193000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.840727000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.941936000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.043336000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.144665000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.245907000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.347090000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.448209000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.549390000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.650567000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.751684000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.851898000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.952826000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:34.053921000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:34.154242000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:34.255396000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:34.356550000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:34.397027000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:231	连接状态变更	{"old_state": "Connecting", "new_state": "Disconnected"}
2025-05-25 15:47:34.397087000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:67	连接控制平面失败:	{"error": "创建连接失败: 连接超时: context deadline exceeded"}
main.main
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:67
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-25 15:49:55.079371000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 15:49:55.101183000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "72f420a6-03dd-4447-8990-8d4812ae23d4"}
2025-05-25 15:49:55.101248000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 15:49:55.101291000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 15:49:55.125461000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-25 15:49:55.125534000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-25 15:49:55.166573000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-25 15:49:55.166646000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-25 15:49:55.166663000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-25 15:49:55.101290000"}
2025-05-25 15:49:55.166683000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-25 15:49:55.125459000"}
2025-05-25 15:49:55.166843000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-25 15:49:55.166875000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-25 15:49:55.166883000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-25 15:49:55.167069000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-25 15:49:55.167134000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-25 15:49:55.167159000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-25 15:49:55.167206000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-25 15:49:55.167252000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-25 15:49:55.167259000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-25 15:50:10.169512000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 2, "dropped": 0}
2025-05-25 15:50:15.171485000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 3, "dropped": 0}
2025-05-25 16:10:45.433677000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-25 16:20:56.094735000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 5, "dropped": 0}
2025-05-25 16:43:06.557702000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 6, "dropped": 0}
2025-05-25 17:05:17.336997000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 7, "dropped": 0}
2025-05-25 17:22:45.430309000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 17:22:45.453019000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "06ae1344-7fff-475d-8aee-6d2cd9df1c53"}
2025-05-25 17:22:45.453117000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 17:22:45.453163000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 17:22:45.496728000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-25 17:22:45.496770000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-25 17:22:45.501751000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-25 17:22:45.501808000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-25 17:22:45.501931000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-25 17:22:45.453162000"}
2025-05-25 17:22:45.501946000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-25 17:22:45.496727000"}
2025-05-25 17:22:45.502062000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-25 17:22:45.502081000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-25 17:22:45.502101000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-25 17:22:45.502153000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-25 17:22:45.502266000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-25 17:22:45.502284000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-25 17:22:45.502330000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-25 17:22:45.502389000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-25 17:22:45.502400000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-25 17:23:00.504221000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 2, "dropped": 0}
2025-05-25 17:23:10.508437000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 3, "dropped": 0}
2025-05-25 17:29:35.756975000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-25 19:54:35.380116000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 19:54:35.402721000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "c34e5940-164e-4f07-8068-e4dafd95c1a5"}
2025-05-25 19:54:35.402781000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 19:54:35.402822000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 19:54:35.656878000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-25 19:54:35.656958000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-25 19:54:36.039552000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-25 19:54:36.039655000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-25 19:54:36.039704000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-25 19:54:35.402822000"}
2025-05-25 19:54:36.039739000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-25 19:54:35.656875000"}
2025-05-25 19:54:36.039749000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-25 19:54:36.039836000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-25 19:54:36.039870000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-25 19:54:36.040334000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-25 19:54:36.040406000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-25 19:54:36.040431000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-25 19:54:36.040479000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-25 19:54:36.040525000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-25 19:54:36.040534000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-25 19:54:51.041742000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 3, "dropped": 0}
2025-05-25 19:54:56.041995000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-25 19:58:19.100789000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:176	接收到退出信号，正在关闭 Agent...
2025-05-25 19:58:19.118019000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:398	日志流上下文取消，正在关闭...
2025-05-25 19:58:19.118081000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:284	任务流正在关闭...
2025-05-25 19:58:19.118090000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:338	指标流上下文取消，正在关闭...
2025-05-25 19:58:19.121095000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:286	所有采集器已停止.
2025-05-25 19:58:19.124310000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:251	停止接收任务配置，流已关闭或上下文已取消:	{"error": "rpc error: code = Canceled desc = context canceled"}
2025-05-25 19:58:26.594116000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 17, "dropped": 0}
2025-05-25 19:58:35.650034000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:225	健康检查失败，开始重连	{"error": "连接状态异常: IDLE"}
2025-05-25 19:58:35.651406000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Reconnecting", "timestamp": "2025-05-25 19:58:35.651388000"}
2025-05-25 19:58:35.651421000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connected", "new_state": "Reconnecting"}
2025-05-25 19:58:35.755748000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 1, "server": "localhost:50051"}
2025-05-25 19:58:45.756832000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 1}
2025-05-25 19:58:46.759117000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 2, "server": "localhost:50051"}
2025-05-25 19:58:56.760045000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 2}
2025-05-25 19:58:58.761936000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 3, "server": "localhost:50051"}
2025-05-25 20:20:58.248413000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 20:20:58.393908000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "e5f30ce7-8f0b-46bb-90ac-d23d6073926f"}
2025-05-25 20:20:58.394039000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 20:20:58.394085000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 20:20:58.399178000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-25 20:20:58.399225000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-25 20:20:58.684487000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-25 20:20:58.684667000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-25 20:20:58.684755000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-25 20:20:58.684796000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-25 20:20:58.684879000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-25 20:20:58.684906000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-25 20:20:58.684796000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-25 20:20:58.394084000"}
2025-05-25 20:20:58.684985000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-25 20:20:58.399177000"}
2025-05-25 20:20:58.685235000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-25 20:20:58.685260000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-25 20:20:58.685341000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-25 20:20:58.685465000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-25 20:20:58.685578000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-25 20:21:13.686747000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 3, "dropped": 0}
2025-05-25 20:21:18.688715000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-25 20:26:40.233120000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:254	接收任务配置失败:	{"error": "rpc error: code = Unavailable desc = error reading from server: EOF"}
main.startTaskStream.func3
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:254
2025-05-25 20:27:12.247042000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 20:27:12.256732000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "d0a9d1e7-3045-432f-9d06-636124456daa"}
2025-05-25 20:27:12.256756000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 20:27:12.256800000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 20:27:12.259834000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-25 20:27:12.259864000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-25 20:27:12.262114000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-25 20:27:12.262171000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-25 20:27:12.262302000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-25 20:27:12.256799000"}
2025-05-25 20:27:12.262417000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-25 20:27:12.259833000"}
2025-05-25 20:27:12.262428000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-25 20:27:12.262480000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-25 20:27:12.262552000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-25 20:27:12.262673000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-25 20:27:12.262829000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-25 20:27:12.262851000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-25 20:27:12.262945000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-25 20:27:12.263120000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-25 20:27:12.263136000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-25 20:27:27.265045000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-25 20:27:32.269162000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 5, "dropped": 0}
2025-05-25 20:32:32.394495000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 6, "dropped": 0}
2025-05-25 20:35:12.443828000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 7, "dropped": 0}
2025-05-25 20:38:52.493497000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 8, "dropped": 0}
2025-05-26 09:19:34.915098000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-26 09:19:34.920611000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "b421ec9c-5a3b-4b52-8193-0e42ba58f063"}
2025-05-26 09:19:34.920717000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 09:19:34.920771000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 09:19:34.925709000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 09:19:34.925787000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 09:19:34.950343000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-26 09:19:34.950510000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 09:19:34.950717000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-26 09:19:34.950721000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-26 09:19:34.950690000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 09:19:34.920770000"}
2025-05-26 09:19:34.950813000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-26 09:19:34.950830000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 09:19:34.925707000"}
2025-05-26 09:19:34.951291000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 09:19:34.951616000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 09:19:34.951663000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 09:19:34.951876000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 09:19:34.951979000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 09:19:34.951993000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 09:19:49.952608000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1, "dropped": 0}
2025-05-26 09:19:54.953656000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 2, "dropped": 0}
2025-05-26 09:20:54.961976000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-26 09:31:15.470641000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 7, "dropped": 0}
2025-05-26 09:33:25.896471000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 9, "dropped": 0}
2025-05-26 09:41:26.378288000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 12, "dropped": 0}
2025-05-26 09:48:06.638632000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 14, "dropped": 0}
2025-05-26 09:53:47.407029000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 15, "dropped": 0}
2025-05-26 09:58:27.873681000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 17, "dropped": 0}
2025-05-26 10:04:58.129663000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 18, "dropped": 0}
2025-05-26 10:08:48.285839000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 20, "dropped": 0}
2025-05-26 10:15:48.659317000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 23, "dropped": 0}
2025-05-26 10:17:48.804985000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 25, "dropped": 0}
2025-05-26 10:19:09.210144000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 27, "dropped": 0}
2025-05-26 10:56:39.773580000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-26 10:56:39.804907000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "bdaf92ef-438d-4798-9aa9-4eb62e979c37"}
2025-05-26 10:56:39.804972000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 10:56:39.805011000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 10:56:39.809799000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 10:56:39.809865000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 10:56:39.857531000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-26 10:56:39.857623000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 10:56:39.857745000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 10:56:39.805010000"}
2025-05-26 10:56:39.857765000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 10:56:39.809797000"}
2025-05-26 10:56:39.857808000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-26 10:56:39.857729000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-26 10:56:39.857890000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 10:56:39.857876000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-26 10:56:39.858109000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 10:56:39.858136000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 10:56:39.858204000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 10:56:39.858257000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 10:56:39.858269000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 10:56:54.859294000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 3, "dropped": 0}
