package main

import (
	"aiops/control_plane/config"
	"aiops/control_plane/internal/database"
	"aiops/control_plane/internal/repository"
	"aiops/control_plane/internal/service"
	"aiops/control_plane/internal/transport/grpc"
	"aiops/control_plane/internal/transport/http"
	cf "aiops/pkg/config"
	"aiops/pkg/log"
	"aiops/plugins/loader"
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"go.uber.org/zap"
)

func main() {
	logConfig := cf.NewConfig("config/control_plane.yaml")
	// 创建日志工具
	logger := log.NewLog(logConfig)
	logger.Info("DevInsight Control Plane 正在启动...")

	// 加载配置
	cfg := config.LoadConfig(logConfig)

	// 初始化数据库
	db, err := database.InitDatabase(cfg.DatabasePath, logger)
	if err != nil {
		logger.Error("初始化数据库失败:", zap.Error(err))
		os.Exit(1)
	}
	logger.Info("数据库初始化成功")

	// 创建仓库
	agentRepo := repository.NewAgentRepository(db)
	deviceRepo := repository.NewDeviceRepository(db)
	taskRepo := repository.NewCollectorTaskRepository(db)
	metricRepo := repository.NewMetricRepository(db)
	alertRuleRepo := repository.NewAlertRuleRepository(db)
	alertEventRepo := repository.NewAlertEventRepository(db)
	userRepo := repository.NewUserRepository(db)
	logRepo := repository.NewLogRepository(db)
	supportedMetricRepo := repository.NewSupportedMetricRepository(db)

	// 创建服务
	agentService := service.NewAgentService(agentRepo, deviceRepo, taskRepo, logger)
	deviceService := service.NewDeviceService(deviceRepo, taskRepo, agentRepo, logger)
	taskService := service.NewCollectorTaskService(taskRepo, deviceRepo, agentRepo, logger)
	alertService := service.NewAlertService(alertRuleRepo, alertEventRepo, deviceRepo, cfg, logger)
	metricService := service.NewMetricService(metricRepo, deviceRepo, alertRuleRepo, alertEventRepo, logger)
	userService := service.NewUserService(userRepo, logger)
	logService := service.NewLogService(logRepo, logger.Named("LogService"))
	supportedMetricService := service.NewSupportedMetricService(supportedMetricRepo, logger.Named("SupportedMetricService"))

	// 加载插件配置
	pluginCfg, err := config.LoadPluginConfig()
	if err != nil {
		logger.Error("加载插件配置失败", zap.Error(err))
		// 使用默认配置
		pluginCfg = &config.PluginConfig{
			Enabled: true,
			PluginDirs: []string{
				"./plugins/build",
				"./plugins/examples",
			},
			AutoLoad:  true,
			WatchMode: false,
		}
	}

	// 初始化插件系统
	var pluginManager *loader.PluginManager
	if pluginCfg.Enabled {
		loaderConfig := &loader.LoaderConfig{
			PluginDirs: pluginCfg.PluginDirs,
			AutoLoad:   pluginCfg.AutoLoad,
			WatchMode:  pluginCfg.WatchMode,
		}
		pluginManager = loader.NewPluginManager(loaderConfig)
		if err := pluginManager.Initialize(context.Background()); err != nil {
			logger.Warn("初始化插件系统失败", zap.Error(err))
		} else {
			logger.Info("插件系统初始化成功")
		}
	} else {
		logger.Info("插件系统已禁用")
	}

	// 创建插件服务
	pluginService := service.NewPluginService(pluginManager, agentRepo, deviceRepo, logger.Logger)

	// 创建插件注册服务
	pluginRegistryService := service.NewPluginRegistryService("./plugins/storage", logger.Logger)

	// 创建插件分发服务
	pluginDistributionService := service.NewPluginDistributionService(
		logger.Logger,
		pluginRegistryService,
		"./plugins/storage",
		100, // 100MB max plugin size
	)

	// 设置服务间依赖
	taskService.SetAgentService(agentService)
	metricService.SetAlertService(alertService)

	// 初始化默认支持的指标
	if err := supportedMetricService.SeedDefaultMetrics(); err != nil {
		logger.Error("初始化默认支持指标失败", zap.Error(err))
	}

	// 初始化管理员用户
	if err := userService.InitAdminUser("admin", "admin123"); err != nil {
		logger.Error("初始化管理员用户失败: %v", zap.Error(err))
	}

	// 创建并启动 gRPC 服务器
	grpcServer := grpc.NewServer(cfg, agentService, metricService, logService, supportedMetricService, pluginDistributionService, logger)
	if err := grpcServer.Start(); err != nil {
		logger.Error("启动 gRPC 服务器失败: %v", zap.Error(err))
		os.Exit(1)
	}

	// 创建并启动 HTTP 服务器
	httpServer := http.NewServer(
		cfg,
		agentService,
		deviceService,
		taskService,
		metricService,
		alertService,
		userService,
		logService,
		supportedMetricService,
		pluginService,
		logger,
	)
	if err := httpServer.Start(); err != nil {
		logger.Error("启动 HTTP 服务器失败: %v", zap.Error(err))
		os.Exit(1)
	}

	// 等待中断信号
	logger.Info("DevInsight Control Plane 已启动")
	logger.Info("HTTP 服务监听于:", zap.String("地址", fmt.Sprintf("http://localhost:%d", cfg.HTTPPort)))
	logger.Info("gRPC 服务监听于:", zap.String("地址", fmt.Sprintf("localhost:%d", cfg.GRPCPort)))
	logger.Info("按 Ctrl+C 退出")

	// 设置信号处理
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	<-sigCh

	// 优雅关闭
	logger.Info("正在关闭服务...")
	// 关闭 HTTP 服务器
	if err := httpServer.Stop(); err != nil {
		logger.Error("关闭 HTTP 服务器失败: %v", zap.Error(err))
	}

	// 关闭 gRPC 服务器
	grpcServer.Stop()

	logger.Info("DevInsight Control Plane 已关闭")
}
