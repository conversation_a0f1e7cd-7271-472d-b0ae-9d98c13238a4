package service

import (
	"context"
	"crypto/md5"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"aiops/pkg/proto"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/emptypb"
)

// PluginDistributionService 处理插件分发相关的 gRPC 请求
type PluginDistributionService struct {
	logger          *zap.Logger
	registryService *PluginRegistryService
	pluginStorePath string
	maxPluginSizeMB int64
	enableStreaming bool
	proto.UnimplementedAgentServiceServer
}

// NewPluginDistributionService 创建新的插件分发服务
func NewPluginDistributionService(
	logger *zap.Logger,
	registryService *PluginRegistryService,
	pluginStorePath string,
	maxPluginSizeMB int64,
) *PluginDistributionService {
	return &PluginDistributionService{
		logger:          logger,
		registryService: registryService,
		pluginStorePath: pluginStorePath,
		maxPluginSizeMB: maxPluginSizeMB,
		enableStreaming: true,
	}
}

// RequestPlugin 处理 Agent 的插件请求
func (s *PluginDistributionService) RequestPlugin(
	ctx context.Context,
	req *proto.PluginRequest,
) (*proto.PluginResponse, error) {
	s.logger.Info("收到插件请求",
		zap.String("agent_id", req.AgentId),
		zap.String("plugin_name", req.PluginName),
		zap.String("plugin_version", req.PluginVersion),
		zap.String("device_type", req.DeviceType),
		zap.String("architecture", req.Architecture),
		zap.String("os", req.Os))

	// 验证请求参数
	if req.AgentId == "" || req.PluginName == "" {
		return &proto.PluginResponse{
			Success: false,
			Message: "agent_id 和 plugin_name 不能为空",
		}, nil
	}

	// 检查插件是否存在和兼容性
	pluginInfo, err := s.registryService.GetPluginForAgent(req.PluginName, req.PluginVersion, req.DeviceType, req.Architecture, req.Os)
	if err != nil {
		s.logger.Error("获取插件信息失败",
			zap.Error(err),
			zap.String("plugin_name", req.PluginName))
		return &proto.PluginResponse{
			Success: false,
			Message: fmt.Sprintf("插件不可用: %v", err),
		}, nil
	}

	// 读取插件二进制文件
	pluginBinary, checksum, err := s.loadPluginBinary(pluginInfo.BinaryPath)
	if err != nil {
		s.logger.Error("加载插件二进制文件失败",
			zap.Error(err),
			zap.String("binary_path", pluginInfo.BinaryPath))
		return &proto.PluginResponse{
			Success: false,
			Message: fmt.Sprintf("加载插件文件失败: %v", err),
		}, nil
	}

	// 构建响应
	response := &proto.PluginResponse{
		Success:  true,
		Message:  "插件分发成功",
		Checksum: checksum,
		Metadata: &proto.PluginMetadata{
			Name:                   pluginInfo.Name,
			Version:                pluginInfo.Version,
			Description:            pluginInfo.Description,
			SupportedDeviceTypes:   pluginInfo.SupportedDeviceTypes,
			SupportedArchitectures: pluginInfo.SupportedArchitectures,
			SupportedOs:            pluginInfo.SupportedOS,
			Configuration:          pluginInfo.Configuration,
			Dependencies:           pluginInfo.Dependencies,
			SizeBytes:              int64(len(pluginBinary)),
			Author:                 pluginInfo.Author,
			CreatedAt:              pluginInfo.CreatedAt.Unix(),
			UpdatedAt:              pluginInfo.UpdatedAt.Unix(),
		},
	}

	// 如果插件较小，直接在响应中包含二进制数据
	if len(pluginBinary) <= int(s.maxPluginSizeMB*1024*1024) {
		response.PluginBinary = pluginBinary
	} else {
		// 对于大文件，提供下载链接
		downloadURL := s.generateDownloadURL(req.AgentId, pluginInfo.Name, pluginInfo.Version)
		response.DownloadUrl = downloadURL
		s.logger.Info("插件文件较大，提供下载链接",
			zap.String("plugin_name", req.PluginName),
			zap.Int("size_bytes", len(pluginBinary)),
			zap.String("download_url", downloadURL))
	}

	// 记录分发事件
	err = s.registryService.RecordDistribution(req.AgentId, pluginInfo.Name, pluginInfo.Version, "success", "")
	if err != nil {
		s.logger.Warn("记录分发事件失败", zap.Error(err))
	}

	return response, nil
}

// StreamPluginUpdates 处理插件更新推送流
func (s *PluginDistributionService) StreamPluginUpdates(
	req *emptypb.Empty,
	stream grpc.ServerStreamingServer[proto.PluginUpdateNotification],
) error {
	s.logger.Info("建立插件更新推送流连接")

	// 这里可以实现推送逻辑
	// 例如：当有新的插件版本时，主动推送给相关的 Agent
	for {
		select {
		case <-stream.Context().Done():
			s.logger.Info("插件更新推送流连接断开")
			return nil
		case <-time.After(30 * time.Second):
			// 定期检查是否有需要推送的更新
			// TODO: 实现实际的更新检查和推送逻辑
		}
	}
}

// ReportPluginStatus 处理 Agent 的插件状态上报
func (s *PluginDistributionService) ReportPluginStatus(
	ctx context.Context,
	req *proto.PluginStatusReport,
) (*proto.PluginStatusResponse, error) {
	s.logger.Info("收到插件状态报告",
		zap.String("agent_id", req.AgentId),
		zap.Int("plugin_count", len(req.PluginStatuses)))

	if req.AgentId == "" {
		return &proto.PluginStatusResponse{
			Success: false,
			Message: "agent_id 不能为空",
		}, nil
	}

	var actions []string

	// 处理每个插件的状态
	for _, pluginStatus := range req.PluginStatuses {
		err := s.registryService.UpdateAgentPluginStatus(
			req.AgentId,
			pluginStatus.PluginName,
			pluginStatus.Version,
			pluginStatus.Status,
			pluginStatus.ErrorMessage,
		)
		if err != nil {
			s.logger.Error("更新插件状态失败",
				zap.Error(err),
				zap.String("agent_id", req.AgentId),
				zap.String("plugin_name", pluginStatus.PluginName))
			continue
		}

		// 根据插件状态决定建议的操作
		switch pluginStatus.Status {
		case "failed":
			actions = append(actions, fmt.Sprintf("reload:%s", pluginStatus.PluginName))
		case "outdated":
			actions = append(actions, fmt.Sprintf("update:%s", pluginStatus.PluginName))
		}
	}

	return &proto.PluginStatusResponse{
		Success: true,
		Message: "状态更新成功",
		Actions: actions,
	}, nil
}

// loadPluginBinary 加载插件二进制文件并计算校验和
func (s *PluginDistributionService) loadPluginBinary(binaryPath string) ([]byte, string, error) {
	// 确保文件路径在允许的存储目录内
	fullPath := filepath.Join(s.pluginStorePath, binaryPath)
	if !s.isPathSafe(fullPath) {
		return nil, "", fmt.Errorf("不安全的文件路径: %s", binaryPath)
	}

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		return nil, "", fmt.Errorf("插件文件不存在: %s", fullPath)
	}

	// 读取文件
	data, err := os.ReadFile(fullPath)
	if err != nil {
		return nil, "", fmt.Errorf("读取插件文件失败: %v", err)
	}

	// 计算 MD5 校验和
	hash := md5.New()
	if _, err := hash.Write(data); err != nil {
		return nil, "", fmt.Errorf("计算校验和失败: %v", err)
	}
	checksum := fmt.Sprintf("%x", hash.Sum(nil))

	return data, checksum, nil
}

// isPathSafe 检查文件路径是否安全（防止路径遍历攻击）
func (s *PluginDistributionService) isPathSafe(path string) bool {
	absStorePath, err := filepath.Abs(s.pluginStorePath)
	if err != nil {
		return false
	}

	absTargetPath, err := filepath.Abs(path)
	if err != nil {
		return false
	}

	// 检查目标路径是否在存储目录内
	rel, err := filepath.Rel(absStorePath, absTargetPath)
	if err != nil {
		return false
	}

	return !filepath.IsAbs(rel) && !filepath.HasPrefix(rel, "..")
}

// generateDownloadURL 生成插件下载链接
func (s *PluginDistributionService) generateDownloadURL(agentID, pluginName, version string) string {
	// 这里应该生成一个安全的下载链接
	// 可以包含临时令牌、过期时间等安全措施
	return fmt.Sprintf("/api/v1/plugins/download/%s/%s/%s?agent=%s&token=%s",
		pluginName, version, agentID, agentID, s.generateDownloadToken(agentID, pluginName, version))
}

// generateDownloadToken 生成下载令牌
func (s *PluginDistributionService) generateDownloadToken(agentID, pluginName, version string) string {
	// 简单的令牌生成逻辑，实际应该使用更安全的方法
	hash := md5.New()
	_, _ = io.WriteString(hash, fmt.Sprintf("%s:%s:%s:%d", agentID, pluginName, version, time.Now().Unix()))
	return fmt.Sprintf("%x", hash.Sum(nil))[:16]
}
