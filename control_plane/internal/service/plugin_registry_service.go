package service

import (
	"context"
	"crypto/sha256"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sync"
	"time"

	"go.uber.org/zap"
)

// PluginDistribution 插件分发信息
type PluginDistribution struct {
	ID           string            `json:"id"`
	Name         string            `json:"name"`
	Version      string            `json:"version"`
	DeviceTypes  []string          `json:"device_types"`
	BinaryPath   string            `json:"binary_path"`
	Checksum     string            `json:"checksum"`
	Size         int64             `json:"size"`
	Dependencies []string          `json:"dependencies"`
	Config       map[string]string `json:"config"`
	CreatedAt    time.Time         `json:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at"`
}

// AgentPluginRequirement Agent插件需求
type AgentPluginRequirement struct {
	AgentID     string    `json:"agent_id"`
	DeviceTypes []string  `json:"device_types"`
	RequestedAt time.Time `json:"requested_at"`
}

// PluginDistributionStatus 插件分发状态
type PluginDistributionStatus struct {
	AgentID      string     `json:"agent_id"`
	PluginID     string     `json:"plugin_id"`
	Status       string     `json:"status"` // requested, downloading, downloaded, installed, failed
	Progress     float64    `json:"progress"`
	ErrorMessage string     `json:"error_message,omitempty"`
	StartedAt    time.Time  `json:"started_at"`
	CompletedAt  *time.Time `json:"completed_at,omitempty"`
}

// PluginRegistryService 插件注册表服务
type PluginRegistryService struct {
	mu                  sync.RWMutex
	plugins             map[string]*PluginDistribution       // pluginID -> distribution
	agentRequirements   map[string]*AgentPluginRequirement   // agentID -> requirement
	distributionStatus  map[string]*PluginDistributionStatus // "agentID:pluginID" -> status
	pluginsByDeviceType map[string][]*PluginDistribution     // deviceType -> plugins
	pluginDir           string
	logger              *zap.Logger

	// 配置
	maxPluginSize          int64
	supportedDeviceTypes   []string
	enableAutoDistribution bool
	cleanupInterval        time.Duration
}

// NewPluginRegistryService 创建新的插件注册表服务
func NewPluginRegistryService(pluginDir string, logger *zap.Logger) *PluginRegistryService {
	service := &PluginRegistryService{
		plugins:                make(map[string]*PluginDistribution),
		agentRequirements:      make(map[string]*AgentPluginRequirement),
		distributionStatus:     make(map[string]*PluginDistributionStatus),
		pluginsByDeviceType:    make(map[string][]*PluginDistribution),
		pluginDir:              pluginDir,
		logger:                 logger,
		maxPluginSize:          100 * 1024 * 1024, // 100MB
		supportedDeviceTypes:   []string{"mysql", "redis", "system", "postgresql", "mongodb"},
		enableAutoDistribution: true,
		cleanupInterval:        24 * time.Hour,
	}

	// 启动后台服务
	go service.startBackgroundServices()

	return service
}

// RegisterPlugin 注册插件到注册表
func (s *PluginRegistryService) RegisterPlugin(ctx context.Context, pluginPath string, metadata *PluginDistribution) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 验证插件文件
	if err := s.validatePluginFile(pluginPath); err != nil {
		return fmt.Errorf("插件文件验证失败: %w", err)
	}

	// 计算文件校验和
	checksum, size, err := s.calculateFileInfo(pluginPath)
	if err != nil {
		return fmt.Errorf("计算文件信息失败: %w", err)
	}

	// 复制插件到插件目录
	pluginFileName := fmt.Sprintf("%s-%s.so", metadata.Name, metadata.Version)
	destPath := filepath.Join(s.pluginDir, pluginFileName)

	if err := s.copyFile(pluginPath, destPath); err != nil {
		return fmt.Errorf("复制插件文件失败: %w", err)
	}

	// 更新元数据
	metadata.BinaryPath = destPath
	metadata.Checksum = checksum
	metadata.Size = size
	metadata.UpdatedAt = time.Now()
	if metadata.CreatedAt.IsZero() {
		metadata.CreatedAt = time.Now()
	}

	// 存储到注册表
	s.plugins[metadata.ID] = metadata

	// 更新设备类型索引
	for _, deviceType := range metadata.DeviceTypes {
		s.pluginsByDeviceType[deviceType] = append(s.pluginsByDeviceType[deviceType], metadata)
	}

	s.logger.Info("插件注册成功",
		zap.String("plugin_id", metadata.ID),
		zap.String("name", metadata.Name),
		zap.String("version", metadata.Version),
		zap.Strings("device_types", metadata.DeviceTypes))

	// 检查是否有Agent需要此插件
	if s.enableAutoDistribution {
		go s.checkAndDistributePlugin(metadata)
	}

	return nil
}

// RequestPlugin Agent请求插件
func (s *PluginRegistryService) RequestPlugin(ctx context.Context, agentID string, deviceTypes []string) ([]*PluginDistribution, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 记录Agent需求
	s.agentRequirements[agentID] = &AgentPluginRequirement{
		AgentID:     agentID,
		DeviceTypes: deviceTypes,
		RequestedAt: time.Now(),
	}

	var requiredPlugins []*PluginDistribution

	// 为每个设备类型查找合适的插件
	for _, deviceType := range deviceTypes {
		plugins := s.pluginsByDeviceType[deviceType]
		if len(plugins) > 0 {
			// 选择最新版本的插件
			latestPlugin := s.getLatestPlugin(plugins)
			if latestPlugin != nil {
				requiredPlugins = append(requiredPlugins, latestPlugin)

				// 记录分发状态
				statusKey := fmt.Sprintf("%s:%s", agentID, latestPlugin.ID)
				s.distributionStatus[statusKey] = &PluginDistributionStatus{
					AgentID:   agentID,
					PluginID:  latestPlugin.ID,
					Status:    "requested",
					Progress:  0.0,
					StartedAt: time.Now(),
				}
			}
		}
	}

	s.logger.Info("Agent请求插件",
		zap.String("agent_id", agentID),
		zap.Strings("device_types", deviceTypes),
		zap.Int("plugins_found", len(requiredPlugins)))

	return requiredPlugins, nil
}

// GetPluginBinary 获取插件二进制文件
func (s *PluginRegistryService) GetPluginBinary(ctx context.Context, agentID, pluginID string) (io.ReadCloser, error) {
	s.mu.RLock()
	plugin, exists := s.plugins[pluginID]
	s.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("插件不存在: %s", pluginID)
	}

	// 更新分发状态
	statusKey := fmt.Sprintf("%s:%s", agentID, pluginID)
	s.updateDistributionStatus(statusKey, "downloading", 0.0, "")

	// 打开文件
	file, err := os.Open(plugin.BinaryPath)
	if err != nil {
		s.updateDistributionStatus(statusKey, "failed", 0.0, err.Error())
		return nil, fmt.Errorf("打开插件文件失败: %w", err)
	}

	s.logger.Info("开始分发插件",
		zap.String("agent_id", agentID),
		zap.String("plugin_id", pluginID),
		zap.String("plugin_path", plugin.BinaryPath))

	return file, nil
}

// UpdateDistributionStatus 更新插件分发状态
func (s *PluginRegistryService) UpdateDistributionStatus(agentID, pluginID, status string, progress float64, errorMsg string) {
	statusKey := fmt.Sprintf("%s:%s", agentID, pluginID)
	s.updateDistributionStatus(statusKey, status, progress, errorMsg)
}

// GetPluginsByDeviceType 根据设备类型获取插件列表
func (s *PluginRegistryService) GetPluginsByDeviceType(deviceType string) []*PluginDistribution {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return s.pluginsByDeviceType[deviceType]
}

// ListAllPlugins 列出所有注册的插件
func (s *PluginRegistryService) ListAllPlugins() []*PluginDistribution {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var plugins []*PluginDistribution
	for _, plugin := range s.plugins {
		plugins = append(plugins, plugin)
	}

	return plugins
}

// GetDistributionStatus 获取分发状态
func (s *PluginRegistryService) GetDistributionStatus(agentID string) []*PluginDistributionStatus {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var statuses []*PluginDistributionStatus
	for _, status := range s.distributionStatus {
		if status.AgentID == agentID {
			statuses = append(statuses, status)
		}
	}

	return statuses
}

// RemovePlugin 移除插件
func (s *PluginRegistryService) RemovePlugin(ctx context.Context, pluginID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	plugin, exists := s.plugins[pluginID]
	if !exists {
		return fmt.Errorf("插件不存在: %s", pluginID)
	}

	// 删除文件
	if err := os.Remove(plugin.BinaryPath); err != nil {
		s.logger.Warn("删除插件文件失败", zap.String("path", plugin.BinaryPath), zap.Error(err))
	}

	// 从注册表中移除
	delete(s.plugins, pluginID)

	// 从设备类型索引中移除
	for _, deviceType := range plugin.DeviceTypes {
		plugins := s.pluginsByDeviceType[deviceType]
		for i, p := range plugins {
			if p.ID == pluginID {
				s.pluginsByDeviceType[deviceType] = append(plugins[:i], plugins[i+1:]...)
				break
			}
		}
	}

	s.logger.Info("插件移除成功", zap.String("plugin_id", pluginID))
	return nil
}

// Extended PluginDistribution for the distribution service
type ExtendedPluginDistribution struct {
	*PluginDistribution
	Description            string            `json:"description"`
	SupportedDeviceTypes   []string          `json:"supported_device_types"`
	SupportedArchitectures []string          `json:"supported_architectures"`
	SupportedOS            []string          `json:"supported_os"`
	Configuration          map[string]string `json:"configuration"`
	Author                 string            `json:"author"`
}

// GetPluginForAgent 根据Agent需求获取合适的插件
func (s *PluginRegistryService) GetPluginForAgent(pluginName, version, deviceType, architecture, os string) (*ExtendedPluginDistribution, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 如果指定了版本，直接查找
	if version != "" {
		pluginID := fmt.Sprintf("%s-%s", pluginName, version)
		if plugin, exists := s.plugins[pluginID]; exists {
			if s.isPluginCompatible(plugin, deviceType, architecture, os) {
				return s.toExtendedDistribution(plugin), nil
			}
		}
		return nil, fmt.Errorf("指定版本的插件不存在或不兼容: %s-%s", pluginName, version)
	}

	// 查找最新的兼容版本
	var latestPlugin *PluginDistribution
	for _, plugin := range s.plugins {
		if plugin.Name == pluginName && s.isPluginCompatible(plugin, deviceType, architecture, os) {
			if latestPlugin == nil || plugin.CreatedAt.After(latestPlugin.CreatedAt) {
				latestPlugin = plugin
			}
		}
	}

	if latestPlugin == nil {
		return nil, fmt.Errorf("未找到兼容的插件: %s (device_type: %s, arch: %s, os: %s)",
			pluginName, deviceType, architecture, os)
	}

	return s.toExtendedDistribution(latestPlugin), nil
}

// RecordDistribution 记录插件分发事件
func (s *PluginRegistryService) RecordDistribution(agentID, pluginName, version, status, errorMessage string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	pluginID := fmt.Sprintf("%s-%s", pluginName, version)
	statusKey := fmt.Sprintf("%s:%s", agentID, pluginID)

	if distribution, exists := s.distributionStatus[statusKey]; exists {
		distribution.Status = status
		distribution.ErrorMessage = errorMessage
		if status == "success" || status == "failed" {
			now := time.Now()
			distribution.CompletedAt = &now
			distribution.Progress = 100.0
		}
	} else {
		now := time.Now()
		s.distributionStatus[statusKey] = &PluginDistributionStatus{
			AgentID:      agentID,
			PluginID:     pluginID,
			Status:       status,
			Progress:     100.0,
			ErrorMessage: errorMessage,
			StartedAt:    now,
			CompletedAt:  &now,
		}
	}

	s.logger.Info("记录插件分发事件",
		zap.String("agent_id", agentID),
		zap.String("plugin_name", pluginName),
		zap.String("version", version),
		zap.String("status", status))

	return nil
}

// UpdateAgentPluginStatus 更新Agent插件状态
func (s *PluginRegistryService) UpdateAgentPluginStatus(agentID, pluginName, version, status, errorMessage string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	pluginID := fmt.Sprintf("%s-%s", pluginName, version)
	statusKey := fmt.Sprintf("%s:%s", agentID, pluginID)

	if distribution, exists := s.distributionStatus[statusKey]; exists {
		distribution.Status = status
		distribution.ErrorMessage = errorMessage
		if status == "loaded" || status == "running" {
			distribution.Progress = 100.0
		}
	} else {
		// 如果状态不存在，创建新的状态记录
		s.distributionStatus[statusKey] = &PluginDistributionStatus{
			AgentID:      agentID,
			PluginID:     pluginID,
			Status:       status,
			Progress:     100.0,
			ErrorMessage: errorMessage,
			StartedAt:    time.Now(),
		}
	}

	s.logger.Debug("更新Agent插件状态",
		zap.String("agent_id", agentID),
		zap.String("plugin_name", pluginName),
		zap.String("version", version),
		zap.String("status", status))

	return nil
}

// isPluginCompatible 检查插件是否与Agent环境兼容
func (s *PluginRegistryService) isPluginCompatible(plugin *PluginDistribution, deviceType, architecture, os string) bool {
	// 检查设备类型兼容性
	if deviceType != "" {
		found := false
		for _, supportedType := range plugin.DeviceTypes {
			if supportedType == deviceType {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// TODO: 检查架构和操作系统兼容性
	// 这里需要扩展 PluginDistribution 结构体来包含架构和操作系统信息
	// 或者从插件的配置中读取这些信息

	return true
}

// toExtendedDistribution 转换为扩展的插件分发信息
func (s *PluginRegistryService) toExtendedDistribution(plugin *PluginDistribution) *ExtendedPluginDistribution {
	return &ExtendedPluginDistribution{
		PluginDistribution:     plugin,
		Description:            fmt.Sprintf("Plugin for %s", plugin.Name),
		SupportedDeviceTypes:   plugin.DeviceTypes,
		SupportedArchitectures: []string{"amd64", "arm64"},             // 默认支持的架构
		SupportedOS:            []string{"linux", "windows", "darwin"}, // 默认支持的操作系统
		Configuration:          plugin.Config,
		Author:                 "System",
	}
}

// 私有方法

func (s *PluginRegistryService) validatePluginFile(pluginPath string) error {
	stat, err := os.Stat(pluginPath)
	if err != nil {
		return fmt.Errorf("文件不存在: %w", err)
	}

	if stat.Size() > s.maxPluginSize {
		return fmt.Errorf("插件文件过大: %d bytes (最大: %d)", stat.Size(), s.maxPluginSize)
	}

	// TODO: 验证插件签名
	// TODO: 验证插件格式

	return nil
}

func (s *PluginRegistryService) calculateFileInfo(filePath string) (string, int64, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", 0, err
	}
	defer file.Close()

	hash := sha256.New()
	size, err := io.Copy(hash, file)
	if err != nil {
		return "", 0, err
	}

	checksum := fmt.Sprintf("%x", hash.Sum(nil))
	return checksum, size, nil
}

func (s *PluginRegistryService) copyFile(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	// 确保目标目录存在
	if err := os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
		return err
	}

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	return err
}

func (s *PluginRegistryService) getLatestPlugin(plugins []*PluginDistribution) *PluginDistribution {
	if len(plugins) == 0 {
		return nil
	}

	latest := plugins[0]
	for _, plugin := range plugins[1:] {
		if plugin.UpdatedAt.After(latest.UpdatedAt) {
			latest = plugin
		}
	}

	return latest
}

func (s *PluginRegistryService) updateDistributionStatus(statusKey, status string, progress float64, errorMsg string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if existingStatus, exists := s.distributionStatus[statusKey]; exists {
		existingStatus.Status = status
		existingStatus.Progress = progress
		existingStatus.ErrorMessage = errorMsg

		if status == "installed" || status == "failed" {
			now := time.Now()
			existingStatus.CompletedAt = &now
		}
	}
}

func (s *PluginRegistryService) checkAndDistributePlugin(plugin *PluginDistribution) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 检查哪些Agent需要这个插件
	for agentID, requirement := range s.agentRequirements {
		for _, deviceType := range requirement.DeviceTypes {
			for _, pluginDeviceType := range plugin.DeviceTypes {
				if deviceType == pluginDeviceType {
					// Agent需要这个插件，发送通知
					s.logger.Info("检测到Agent需要新插件",
						zap.String("agent_id", agentID),
						zap.String("plugin_id", plugin.ID),
						zap.String("device_type", deviceType))

					// TODO: 发送插件分发通知给Agent
					break
				}
			}
		}
	}
}

func (s *PluginRegistryService) startBackgroundServices() {
	// 定期清理过期的分发状态
	cleanupTicker := time.NewTicker(s.cleanupInterval)
	defer cleanupTicker.Stop()

	for range cleanupTicker.C {
		s.cleanupExpiredStatuses()
	}
}

func (s *PluginRegistryService) cleanupExpiredStatuses() {
	s.mu.Lock()
	defer s.mu.Unlock()

	now := time.Now()
	expireTime := 7 * 24 * time.Hour // 7天

	for key, status := range s.distributionStatus {
		if status.CompletedAt != nil && now.Sub(*status.CompletedAt) > expireTime {
			delete(s.distributionStatus, key)
		} else if status.CompletedAt == nil && now.Sub(status.StartedAt) > expireTime {
			// 未完成的状态超过7天也清理
			delete(s.distributionStatus, key)
		}
	}

	s.logger.Debug("清理过期的插件分发状态")
}
