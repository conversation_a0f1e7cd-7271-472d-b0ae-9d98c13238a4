# AIOps 插件系统架构重构方案

## 问题分析

当前架构存在的核心问题：
1. **插件加载位置错误**：插件在 control_plane 加载，但执行在 agent 端
2. **资源浪费**：control_plane 加载了无法直接执行的插件
3. **扩展性差**：难以支持分布式环境下的插件动态分发

## 解决方案

### 1. Control Plane 重构
```go
// 插件注册中心 - 只存储元数据，不加载插件
type PluginRegistry struct {
    plugins map[string]*PluginMetadata
    storage PluginStorage
}

type PluginMetadata struct {
    ID              string
    Name            string
    Version         string
    DeviceTypes     []string
    BinaryPath      string
    Checksum        string
    Dependencies    []string
    LastUpdated     time.Time
}

// 插件分发服务
type PluginDistributor struct {
    registry *PluginRegistry
    storage  PluginStorage
}

func (pd *PluginDistributor) DistributePlugin(agentID, pluginID string) (*PluginPackage, error) {
    // 获取插件二进制文件和配置
    // 返回给 agent 进行本地加载
}
```

### 2. Agent 端插件管理器
```go
// Agent 端插件管理器
type AgentPluginManager struct {
    loadedPlugins map[string]Plugin
    pluginCache   *PluginCache
    loader        *PluginLoader
}

func (apm *AgentPluginManager) LoadPlugin(metadata *PluginMetadata, binary []byte) error {
    // 验证插件完整性
    // 动态加载插件
    // 注册到本地采集器
}

func (apm *AgentPluginManager) UnloadPlugin(pluginID string) error {
    // 安全卸载插件
    // 清理资源
}
```

### 3. 集成到现有 CollectorManager
```go
// 增强 agent/internal/collector.go
type CollectorManager struct {
    // 现有字段...
    pluginManager *AgentPluginManager
    
    // 插件采集器
    pluginCollectors map[string]PluginCollector
}

func (m *CollectorManager) StartCollector(config *proto.CollectorTaskConfig, callback func(*proto.TaskStatus)) {
    // 1. 检查是否需要插件
    if m.needsPlugin(config.DeviceType) {
        if err := m.ensurePluginLoaded(config.DeviceType); err != nil {
            callback(&proto.TaskStatus{Status: "failed", Message: err.Error()})
            return
        }
    }
    
    // 2. 使用插件或内置逻辑执行采集
    if collector, exists := m.pluginCollectors[config.DeviceType]; exists {
        m.startPluginCollector(collector, config, callback)
    } else {
        m.startBuiltinCollector(config, callback)
    }
}
```

### 4. 通信协议扩展
```protobuf
// 扩展 gRPC 服务
service PluginService {
    // 插件查询和分发
    rpc GetRequiredPlugins(PluginRequest) returns (PluginResponse);
    rpc DownloadPlugin(PluginDownloadRequest) returns (stream PluginChunk);
    
    // 插件状态报告
    rpc ReportPluginStatus(PluginStatusReport) returns (Empty);
}

message PluginRequest {
    string agent_id = 1;
    repeated string device_types = 2;
}

message PluginResponse {
    repeated PluginMetadata plugins = 1;
}
```

## 实施路径

### 阶段一：基础设施准备（1-2周）
1. 创建 PluginRegistry 和 PluginDistributor
2. 实现 Agent 端 PluginManager
3. 扩展 gRPC 通信协议

### 阶段二：集成现有系统（2-3周）
1. 修改 CollectorManager 支持插件
2. 实现插件自动分发机制
3. 保持向后兼容性

### 阶段三：迁移和优化（2-4周）
1. 逐步迁移现有采集器到插件模式
2. 添加热插拔支持
3. 性能优化和监控

## 技术细节

### 插件生命周期管理
```go
type PluginLifecycle struct {
    Install   func(*PluginMetadata) error
    Load      func() error
    Start     func() error
    Stop      func() error
    Unload    func() error
    Uninstall func() error
}
```

### 安全考虑
1. **插件验证**：数字签名验证
2. **沙箱执行**：隔离插件运行环境
3. **权限控制**：限制插件访问范围
4. **资源限制**：CPU/内存使用限制

### 性能优化
1. **插件缓存**：本地缓存常用插件
2. **延迟加载**：按需加载插件
3. **插件池**：复用插件实例
4. **并发控制**：限制同时加载的插件数量

## 配置示例

### Control Plane 配置
```yaml
plugin_registry:
  storage_type: "file"  # file, database, s3
  storage_path: "/data/plugins"
  auto_discover: true
  
plugin_distributor:
  max_concurrent_downloads: 10
  retry_attempts: 3
  timeout: 30s
```

### Agent 配置
```yaml
plugin_manager:
  cache_dir: "/tmp/plugins"
  max_cache_size: "1GB"
  auto_update: true
  load_timeout: 10s
  
collector_manager:
  prefer_plugins: true
  fallback_to_builtin: true
```

## 优势

### 现有方案问题解决
✅ **执行位置正确**：插件在数据采集的实际位置（agent）加载和执行  
✅ **资源优化**：control_plane 不再加载无用插件  
✅ **扩展性强**：支持大规模分布式部署  
✅ **灵活性高**：支持动态插件更新和热插拔  

### 新增能力
✅ **按需分发**：只向需要的 agents 分发相关插件  
✅ **版本管理**：支持插件版本控制和回滚  
✅ **依赖管理**：自动解析和安装插件依赖  
✅ **监控诊断**：全面的插件运行状态监控  

## 风险评估

### 高风险
- **兼容性**：需要确保与现有系统的兼容性
- **稳定性**：插件动态加载可能影响系统稳定性

### 低风险  
- **性能**：合理的设计不应显著影响性能
- **安全**：通过沙箱和验证机制可以控制风险

### 缓解措施
1. **渐进式迁移**：保持现有系统运行，逐步切换
2. **全面测试**：单元测试、集成测试、压力测试
3. **监控告警**：实时监控插件运行状态
4. **回滚机制**：快速回滚到稳定版本
