package main

import (
	"context"
	"flag"
	"fmt"
	"io" // Required for io.EOF
	"os"
	"os/signal"
	"path/filepath"
	"sync"
	"syscall"
	"time"

	"aiops/agent/internal"
	"aiops/agent/internal/config"
	cf "aiops/pkg/config"
	"aiops/pkg/log"
	pb "aiops/pkg/proto" // Correct alias

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"  // Required for codes.Unavailable
	"google.golang.org/grpc/status" // Required for status.Code
)

var (
	serverAddr = flag.String("server", "localhost:50051", "控制平面服务器地址")
	agentID    = flag.String("agent-id", "", "代理ID，如果为空则自动生成")
)

func main() {
	flag.Parse()

	cfg := config.New()
	if *agentID != "" {
		cfg.AgentID = *agentID
	}
	logConfig := cf.NewConfig("config/agent_config.yaml")

	log := log.NewLog(logConfig)
	log.Info("Agent 启动中...")
	log.Info("Agent ID:",
		zap.String("agent_id", cfg.AgentID))

	// 使用配置中的服务器地址，如果命令行指定了则优先使用命令行参数
	serverAddress := cfg.Connection.ServerAddr
	if *serverAddr != "localhost:50051" { // 如果命令行参数不是默认值
		serverAddress = *serverAddr
	}

	log.Info("控制平面地址:",
		zap.String("server_address", serverAddress))

	// 创建连接管理器
	connConfig := internal.ConnectionConfig{
		ServerAddr:          serverAddress,
		InitialBackoff:      cfg.Connection.InitialBackoff,
		MaxBackoff:          cfg.Connection.MaxBackoff,
		BackoffMultiplier:   cfg.Connection.BackoffMultiplier,
		MaxRetries:          cfg.Connection.MaxRetries,
		HealthCheckInterval: cfg.Connection.HealthCheckInterval,
		ConnectTimeout:      cfg.Connection.ConnectTimeout,
	}
	connMgr := internal.NewConnectionManager(connConfig, log)

	// 建立连接
	if err := connMgr.Connect(); err != nil {
		log.Error("连接控制平面失败:",
			zap.Error(err))
		return
	}
	defer connMgr.Close()

	// 获取客户端
	client, err := connMgr.GetClient()
	if err != nil {
		log.Error("获取客户端失败:",
			zap.Error(err))
		return
	}

	if err := registerAgent(client, cfg, log); err != nil {
		log.Error("注册 Agent 失败:",
			zap.Error(err))
		return
	}
	log.Info("Agent 注册成功")

	// 创建插件客户端和管理器
	pluginDir := filepath.Join(".", "plugins", "cache")
	conn, err := connMgr.GetConnection()
	if err != nil {
		log.Error("获取gRPC连接失败:", zap.Error(err))
		return
	}
	pluginClient := internal.NewPluginClient(log.Logger, conn, cfg.AgentID, pluginDir)
	pluginManager := internal.NewAgentPluginManager(pluginDir, log.Logger, pluginClient)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	var wg sync.WaitGroup

	// Create the metric channel, buffered
	metricCh := make(chan *pb.MetricData, 100)

	// Initialize log forwarder with device ID
	logForwarder := internal.NewLogForwarder(log, cfg.GetDeviceID())

	// Get log channel from forwarder
	logCh := logForwarder.GetLogChannel()

	// Start log forwarder if enabled
	if cfg.IsLogForwarderEnabled() {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := logForwarder.Start(ctx); err != nil {
				log.Error("日志转发器启动失败:", zap.Error(err))
			}
		}()
	}

	// 监听连接事件
	wg.Add(1)
	go func() {
		defer wg.Done()
		for event := range connMgr.GetEventChannel() {
			log.Info("连接状态变更事件",
				zap.String("state", event.State.String()),
				zap.Time("timestamp", event.Timestamp))

			if event.Error != nil {
				log.Error("连接错误", zap.Error(event.Error))
			}

			// 如果重新连接成功，重新获取客户端
			if event.State == internal.Connected {
				newClient, err := connMgr.GetClient()
				if err == nil {
					client = newClient
				}
			}
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		// Pass metricCh to startTaskStream
		if err := startTaskStream(ctx, client, cfg, log, metricCh, pluginManager); err != nil {
			log.Error("任务流错误:",
				zap.Error(err))
			cancel() // If task stream fails, cancel other goroutines
		}
	}()

	// 启动插件更新流监听
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := startPluginUpdateStream(ctx, pluginClient, log); err != nil {
			log.Error("插件更新流错误:",
				zap.Error(err))
			// Plugin update stream errors shouldn't cancel other goroutines
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		// Pass metricCh to startMetricStream
		if err := startMetricStream(ctx, client, cfg, log, metricCh); err != nil {
			log.Error("指标流错误:",
				zap.Error(err))
			cancel() // If metric stream fails, cancel other goroutines
		}
	}()

	// Add log stream if log forwarder is enabled
	if cfg.IsLogForwarderEnabled() {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := startLogStream(ctx, client, cfg, log, logCh); err != nil {
				log.Error("日志流错误:",
					zap.Error(err))
				cancel() // If log stream fails, cancel other goroutines
			}
		}()
	}

	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	select {
	case <-sigCh:
		log.Info("接收到退出信号，正在关闭 Agent...")
		cancel() // Send cancellation signal
	case <-ctx.Done():
		log.Info("上下文已取消，正在关闭 Agent...")
	}

	wg.Wait()
	close(metricCh) // Close metricCh after all goroutines using it have finished
	log.Info("Agent 已关闭")
}

// registerAgent 注册 Agent 到控制平面
func registerAgent(client pb.AgentServiceClient, cfg *config.Config, log *log.Logger) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	resp, err := client.RegisterAgent(ctx, &pb.RegisterAgentRequest{
		AgentId:                 cfg.AgentID,
		AgentIp:                 cfg.AgentIP,
		SupportedCollectorTypes: cfg.SupportedCollectorTypes,
	})
	if err != nil {
		log.Error("注册 Agent 失败:", zap.Error(err))
		return fmt.Errorf("注册 Agent 失败: %w", err)
	}
	if !resp.Success {
		return fmt.Errorf("注册 Agent 失败: %s", resp.Message)
	}
	return nil
}

// startTaskStream 启动任务流，接收任务配置并上报任务状态
// Added metricCh parameter and pluginManager
func startTaskStream(ctx context.Context, client pb.AgentServiceClient, cfg *config.Config, log *log.Logger, metricCh chan *pb.MetricData, pluginManager *internal.AgentPluginManager) error {
	stream, err := client.StreamCollectorTasks(ctx)
	if err != nil {
		return fmt.Errorf("启动任务流失败: %w", err)
	}
	defer func() {
		if err := stream.CloseSend(); err != nil {
			log.Error("关闭任务流失败:",
				zap.Error(err))
		}
	}()

	log.Info("任务流已连接")

	// 初始化增强的采集器管理器，传递插件管理器
	collectorMgr := internal.NewManagerAdapter(log, pluginManager)

	collectorMgr.SetStatusCallback(func(status *pb.TaskStatus) {
		if stream.Context().Err() != nil { // Check if stream context is done
			log.Info("任务流上下文已关闭，无法发送状态 (manager callback):",
				zap.String("TaskId", status.TaskId)) // Changed from log.Warn
			return
		}
		if err := stream.Send(status); err != nil {
			log.Error("上报任务状态失败 (manager callback):",
				zap.Error(err))
		}
	})

	initialStatus := &pb.TaskStatus{
		TaskId: cfg.AgentID, // Use AgentID as initial identifier
		Status: "connected",
	}
	if err := stream.Send(initialStatus); err != nil {
		return fmt.Errorf("发送初始任务状态失败: %w", err)
	}

	// Goroutine to receive task configurations
	go func() {
		for {
			taskConfig, errRecv := stream.Recv()
			if errRecv != nil {
				if ctx.Err() != nil || errRecv == io.EOF || status.Code(errRecv) == codes.Canceled {
					log.Info("停止接收任务配置，流已关闭或上下文已取消:",
						zap.Error(errRecv))
				} else {
					log.Error("接收任务配置失败:",
						zap.Error(errRecv))
				}
				return // Exit goroutine on Recv error or context cancellation
			}
			log.Info("接收到任务配置:",
				zap.String("TaskId", taskConfig.TaskId),
				zap.String("DeviceName", taskConfig.DeviceName),
				zap.Bool("IsEnabled", taskConfig.IsEnabled))

			if taskConfig.IsEnabled {
				collectorMgr.StartCollector(taskConfig, func(status *pb.TaskStatus) {
					if stream.Context().Err() != nil { // Check if stream context is done
						log.Info("任务流上下文已关闭，无法发送状态 (collector):",
							zap.String("TaskId", status.TaskId)) // Changed from log.Warn
						return
					}
					if errSend := stream.Send(status); errSend != nil {
						log.Error("上报任务状态失败 (collector):",
							zap.String("TaskId", status.TaskId),
							zap.Error(errSend))
					}
				})
			} else {
				collectorMgr.StopCollector(taskConfig.TaskId)
			}
		}
	}()

	<-ctx.Done() // Wait for context cancellation
	log.Info("任务流正在关闭...")
	collectorMgr.StopAll() // Ensure all collectors are stopped
	log.Info("所有采集器已停止.")
	return nil
}

// startMetricStream 启动指标流，上报采集到的指标数据
// Added metricCh parameter
func startMetricStream(ctx context.Context, client pb.AgentServiceClient, cfg *config.Config, log *log.Logger, metricCh chan *pb.MetricData) error {
	stream, err := client.StreamMetricData(ctx)
	if err != nil {
		return fmt.Errorf("启动指标流失败: %w", err)
	}

	log.Info("指标流已连接")

	for {
		select {
		case metric, ok := <-metricCh:
			if !ok {
				log.Info("指标通道已关闭，正在关闭指标流...")
				resp, errClose := stream.CloseAndRecv()
				if errClose != nil && status.Code(errClose) != codes.Canceled {
					log.Error("关闭指标流失败 (metric channel closed):",
						zap.Error(errClose))
				} else if resp != nil {
					log.Info("指标流响应 (metric channel closed):",
						zap.String("Message", resp.Message),
						zap.Int32("ReceivedCount", resp.ReceivedCount))
				}
				return nil
			}
			if stream.Context().Err() != nil { // Check if stream context is done
				log.Info("指标流上下文已关闭，无法发送指标:",
					zap.String("MetricKey", metric.MetricKey)) // Changed from log.Warn
				// Do not return here, let ctx.Done() handle the stream closure
				continue
			}
			if errSend := stream.Send(metric); errSend != nil {
				if status.Code(errSend) == codes.Unavailable || status.Code(errSend) == codes.Canceled || errSend == io.EOF {
					log.Error("指标流发送失败，远端关闭或不可用:",
						zap.Error(errSend))
					_, errCloseAndRecv := stream.CloseAndRecv()
					if errCloseAndRecv != nil && status.Code(errCloseAndRecv) != codes.Canceled && errCloseAndRecv != io.EOF {
						log.Error("关闭指标流并接收响应时出错:",
							zap.Error(errCloseAndRecv))
					}
					return fmt.Errorf("指标流发送失败: %w", errSend)
				} else {
					log.Error("上报指标数据失败:",
						zap.Error(errSend))
				}
			}
		case <-ctx.Done():
			log.Info("指标流上下文取消，正在关闭...")
			resp, errClose := stream.CloseAndRecv() // Gracefully close the stream
			if errClose != nil && status.Code(errClose) != codes.Canceled {
				log.Error("关闭指标流失败 (ctx.Done):",
					zap.Error(errClose))
			} else if resp != nil {
				log.Info("指标流响应 (ctx.Done):",
					zap.String("Message", resp.Message),
					zap.Int32("ReceivedCount", resp.ReceivedCount))
			}
			return nil
		}
	}
}

// startLogStream 启动日志流，上报采集到的日志数据
func startLogStream(ctx context.Context, client pb.AgentServiceClient, cfg *config.Config, log *log.Logger, logCh chan *pb.LogEntry) error {
	stream, err := client.StreamLogData(ctx)
	if err != nil {
		return fmt.Errorf("启动日志流失败: %w", err)
	}

	log.Info("日志流已连接")

	for {
		select {
		case logEntry, ok := <-logCh:
			if !ok {
				log.Info("日志通道已关闭，正在关闭日志流...")
				resp, errClose := stream.CloseAndRecv()
				if errClose != nil && status.Code(errClose) != codes.Canceled {
					log.Error("关闭日志流失败 (log channel closed):",
						zap.Error(errClose))
				} else if resp != nil {
					log.Info("日志流响应 (log channel closed):",
						zap.String("Message", resp.Message))
				}
				return nil
			}
			if stream.Context().Err() != nil {
				log.Info("日志流上下文已关闭，无法发送日志:",
					zap.String("Source", logEntry.Source))
				continue
			}
			if errSend := stream.Send(logEntry); errSend != nil {
				if status.Code(errSend) == codes.Unavailable || status.Code(errSend) == codes.Canceled || errSend == io.EOF {
					log.Error("日志流发送失败，远端关闭或不可用:",
						zap.Error(errSend))
					_, errCloseAndRecv := stream.CloseAndRecv()
					if errCloseAndRecv != nil && status.Code(errCloseAndRecv) != codes.Canceled && errCloseAndRecv != io.EOF {
						log.Error("关闭日志流并接收响应时出错:",
							zap.Error(errCloseAndRecv))
					}
					return fmt.Errorf("日志流发送失败: %w", errSend)
				} else {
					log.Error("上报日志数据失败:",
						zap.Error(errSend))
				}
			}
		case <-ctx.Done():
			log.Info("日志流上下文取消，正在关闭...")
			resp, errClose := stream.CloseAndRecv()
			if errClose != nil && status.Code(errClose) != codes.Canceled {
				log.Error("关闭日志流失败 (ctx.Done):",
					zap.Error(errClose))
			} else if resp != nil {
				log.Info("日志流响应 (ctx.Done):",
					zap.String("Message", resp.Message))
			}
			return nil
		}
	}
}

// startPluginUpdateStream 启动插件更新流监听
func startPluginUpdateStream(ctx context.Context, pluginClient *internal.PluginClient, log *log.Logger) error {
	log.Info("启动插件更新流监听")

	// 创建插件更新处理器
	updateHandler := func(notification *pb.PluginUpdateNotification) {
		log.Info("收到插件更新通知",
			zap.String("plugin_name", notification.PluginName),
			zap.String("new_version", notification.NewVersion),
			zap.Bool("is_mandatory", notification.IsMandatory))

		// TODO: 实现插件更新逻辑
		// 可以在这里调用pluginManager的更新方法
	}

	// 启动插件更新流
	if err := pluginClient.StartPluginUpdateStream(ctx, updateHandler); err != nil {
		return fmt.Errorf("启动插件更新流失败: %w", err)
	}

	return nil
}
