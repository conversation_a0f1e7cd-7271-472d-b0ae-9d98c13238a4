package internal

import (
	"context"
	"crypto/md5"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"path/filepath"
	"runtime"
	"time"

	"aiops/pkg/proto"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/emptypb"
)

// PluginClient 插件gRPC客户端
type PluginClient struct {
	logger    *zap.Logger
	client    proto.AgentServiceClient
	agentID   string
	conn      *grpc.ClientConn
	pluginDir string
}

// NewPluginClient 创建新的插件客户端
func NewPluginClient(logger *zap.Logger, conn *grpc.ClientConn, agentID, pluginDir string) *PluginClient {
	return &PluginClient{
		logger:    logger,
		client:    proto.NewAgentServiceClient(conn),
		agentID:   agentID,
		conn:      conn,
		pluginDir: pluginDir,
	}
}

// RequestPlugin 向控制平面请求插件
func (c *PluginClient) RequestPlugin(ctx context.Context, pluginName, version, deviceType string) (*PluginBinary, error) {
	c.logger.Info("请求插件",
		zap.String("plugin_name", pluginName),
		zap.String("version", version),
		zap.String("device_type", deviceType))

	req := &proto.PluginRequest{
		AgentId:       c.agentID,
		PluginName:    pluginName,
		PluginVersion: version,
		DeviceType:    deviceType,
		Architecture:  runtime.GOARCH,
		Os:            runtime.GOOS,
	}

	resp, err := c.client.RequestPlugin(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("请求插件失败: %w", err)
	}

	if !resp.Success {
		return nil, fmt.Errorf("插件请求被拒绝: %s", resp.Message)
	}

	// 如果响应中包含二进制数据，直接使用
	if len(resp.PluginBinary) > 0 {
		return c.processPluginBinary(resp)
	}

	// 如果提供了下载链接，执行下载
	if resp.DownloadUrl != "" {
		return c.downloadPluginFromURL(ctx, resp.DownloadUrl, resp.Metadata, resp.Checksum)
	}

	return nil, fmt.Errorf("插件响应中没有二进制数据或下载链接")
}

// ReportPluginStatus 向控制平面报告插件状态
func (c *PluginClient) ReportPluginStatus(ctx context.Context, statuses []*PluginStatusInfo) error {
	if len(statuses) == 0 {
		return nil
	}

	c.logger.Debug("报告插件状态", zap.Int("plugin_count", len(statuses)))

	protoStatuses := make([]*proto.PluginStatus, 0, len(statuses))
	for _, status := range statuses {
		protoStatus := &proto.PluginStatus{
			PluginName:        status.PluginName,
			Version:           status.Version,
			Status:            status.Status,
			ErrorMessage:      status.ErrorMessage,
			LastUsedTimestamp: status.LastUsedTimestamp,
			LoadTimestamp:     status.LoadTimestamp,
			Metrics:           status.Metrics,
		}
		protoStatuses = append(protoStatuses, protoStatus)
	}

	req := &proto.PluginStatusReport{
		AgentId:        c.agentID,
		PluginStatuses: protoStatuses,
	}

	resp, err := c.client.ReportPluginStatus(ctx, req)
	if err != nil {
		return fmt.Errorf("报告插件状态失败: %w", err)
	}

	if !resp.Success {
		c.logger.Warn("插件状态报告失败", zap.String("message", resp.Message))
	}

	// 处理控制平面建议的操作
	if len(resp.Actions) > 0 {
		c.logger.Info("收到插件操作建议", zap.Strings("actions", resp.Actions))
		// TODO: 实现操作处理逻辑
	}

	return nil
}

// StartPluginUpdateStream 启动插件更新流监听
func (c *PluginClient) StartPluginUpdateStream(ctx context.Context, updateHandler func(*proto.PluginUpdateNotification)) error {
	c.logger.Info("启动插件更新流监听")

	stream, err := c.client.StreamPluginUpdates(ctx, &emptypb.Empty{})
	if err != nil {
		return fmt.Errorf("创建插件更新流失败: %w", err)
	}

	// 接收更新通知
	for {
		notification, err := stream.Recv()
		if err != nil {
			if err == io.EOF {
				c.logger.Info("插件更新流结束")
				break
			}
			return fmt.Errorf("接收插件更新通知失败: %w", err)
		}

		c.logger.Info("收到插件更新通知",
			zap.String("plugin_name", notification.PluginName),
			zap.String("new_version", notification.NewVersion),
			zap.Bool("is_mandatory", notification.IsMandatory))

		if updateHandler != nil {
			updateHandler(notification)
		}
	}

	return nil
}

// processPluginBinary 处理插件二进制数据
func (c *PluginClient) processPluginBinary(resp *proto.PluginResponse) (*PluginBinary, error) {
	// 验证校验和
	if err := c.verifyChecksum(resp.PluginBinary, resp.Checksum); err != nil {
		return nil, fmt.Errorf("插件校验和验证失败: %w", err)
	}

	// 保存插件到本地
	pluginPath := filepath.Join(c.pluginDir, fmt.Sprintf("%s-%s.so", resp.Metadata.Name, resp.Metadata.Version))
	if err := c.savePluginBinary(resp.PluginBinary, pluginPath); err != nil {
		return nil, fmt.Errorf("保存插件失败: %w", err)
	}

	return &PluginBinary{
		Name:         resp.Metadata.Name,
		Version:      resp.Metadata.Version,
		Path:         pluginPath,
		Checksum:     resp.Checksum,
		Size:         resp.Metadata.SizeBytes,
		Metadata:     resp.Metadata,
		DownloadedAt: time.Now(),
	}, nil
}

// downloadPluginFromURL 从URL下载插件
func (c *PluginClient) downloadPluginFromURL(ctx context.Context, url string, metadata *proto.PluginMetadata, expectedChecksum string) (*PluginBinary, error) {
	c.logger.Info("从URL下载插件", zap.String("url", url))

	// TODO: 实现HTTP下载逻辑
	// 这里应该实现安全的HTTP客户端下载逻辑
	// 包括认证、重试、进度报告等

	return nil, fmt.Errorf("URL下载功能暂未实现")
}

// verifyChecksum 验证文件校验和
func (c *PluginClient) verifyChecksum(data []byte, expectedChecksum string) error {
	hash := md5.New()
	if _, err := hash.Write(data); err != nil {
		return err
	}
	actualChecksum := fmt.Sprintf("%x", hash.Sum(nil))

	if actualChecksum != expectedChecksum {
		return fmt.Errorf("校验和不匹配: 期望 %s, 实际 %s", expectedChecksum, actualChecksum)
	}

	return nil
}

// savePluginBinary 保存插件二进制文件
func (c *PluginClient) savePluginBinary(data []byte, path string) error {
	// 确保目录存在
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建插件目录失败: %w", err)
	}

	// 写入文件
	if err := ioutil.WriteFile(path, data, 0755); err != nil {
		return fmt.Errorf("写入插件文件失败: %w", err)
	}

	c.logger.Info("插件保存成功", zap.String("path", path), zap.Int("size", len(data)))
	return nil
}

// PluginBinary 插件二进制信息
type PluginBinary struct {
	Name         string                `json:"name"`
	Version      string                `json:"version"`
	Path         string                `json:"path"`
	Checksum     string                `json:"checksum"`
	Size         int64                 `json:"size"`
	Metadata     *proto.PluginMetadata `json:"metadata"`
	DownloadedAt time.Time             `json:"downloaded_at"`
}

// PluginStatusInfo 插件状态信息
type PluginStatusInfo struct {
	PluginName        string            `json:"plugin_name"`
	Version           string            `json:"version"`
	Status            string            `json:"status"`
	ErrorMessage      string            `json:"error_message,omitempty"`
	LastUsedTimestamp int64             `json:"last_used_timestamp"`
	LoadTimestamp     int64             `json:"load_timestamp"`
	Metrics           map[string]string `json:"metrics,omitempty"`
}
