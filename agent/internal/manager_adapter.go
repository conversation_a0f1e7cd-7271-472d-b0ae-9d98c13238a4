package internal

import (
	"aiops/pkg/log"
	"aiops/pkg/proto"
)

// ManagerAdapter 适配器，将新的 CollectorManager 包装成旧的接口
type ManagerAdapter struct {
	manager *CollectorManager
}

// NewManagerAdapter 创建管理器适配器
func NewManagerAdapter(logger *log.Logger, pluginManager *AgentPluginManager) *ManagerAdapter {
	return &ManagerAdapter{
		manager: NewCollectorManagerWithPluginManager(logger.Logger, nil, pluginManager),
	}
}

// SetStatusCallback 设置状态回调
func (a *ManagerAdapter) SetStatusCallback(callback func(*proto.TaskStatus)) {
	// 在新的系统中，状态回调通过StartCollector传递，这里可以保留为空实现
}

// StartCollector 启动采集器
func (a *ManagerAdapter) StartCollector(taskConfig *proto.CollectorTaskConfig, callback func(*proto.TaskStatus)) {
	a.manager.StartCollector(taskConfig, callback)
}

// StopCollector 停止采集器
func (a *ManagerAdapter) StopCollector(taskID string) {
	a.manager.StopCollector(taskID)
}

// StopAll 停止所有采集器
func (a *ManagerAdapter) StopAll() {
	a.manager.StopAll()
}
